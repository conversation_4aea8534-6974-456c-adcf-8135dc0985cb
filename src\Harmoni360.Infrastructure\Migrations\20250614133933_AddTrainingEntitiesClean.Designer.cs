﻿// <auto-generated />
using System;
using Harmoni360.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace Harmoni360.Infrastructure.Migrations
{
    [DbContext(typeof(ApplicationDbContext))]
    [Migration("20250614133933_AddTrainingEntitiesClean")]
    partial class AddTrainingEntitiesClean
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.1")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("Harmoni360.Domain.Entities.Audits.Audit", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int?>("AchievedPoints")
                        .HasColumnType("integer");

                    b.Property<int?>("ActualDurationMinutes")
                        .HasColumnType("integer");

                    b.Property<string>("AuditNumber")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<int>("AuditorId")
                        .HasColumnType("integer");

                    b.Property<int>("Category")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("CompletedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(450)
                        .HasColumnType("character varying(450)");

                    b.Property<int?>("DepartmentId")
                        .HasColumnType("integer");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<int?>("EstimatedDurationMinutes")
                        .HasColumnType("integer");

                    b.Property<int?>("FacilityId")
                        .HasColumnType("integer");

                    b.Property<bool>("IsRegulatory")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(450)
                        .HasColumnType("character varying(450)");

                    b.Property<int?>("LocationId")
                        .HasColumnType("integer");

                    b.Property<int?>("OverallScore")
                        .HasColumnType("integer");

                    b.Property<int>("Priority")
                        .HasColumnType("integer");

                    b.Property<string>("Recommendations")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<string>("RegulatoryReference")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<int>("RiskLevel")
                        .HasColumnType("integer");

                    b.Property<DateTime>("ScheduledDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<decimal?>("ScorePercentage")
                        .HasPrecision(5, 2)
                        .HasColumnType("numeric(5,2)");

                    b.Property<string>("StandardsApplied")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<DateTime?>("StartedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<string>("Summary")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<int?>("TotalPossiblePoints")
                        .HasColumnType("integer");

                    b.Property<int>("Type")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("AuditNumber")
                        .IsUnique();

                    b.HasIndex("AuditorId");

                    b.HasIndex("Category");

                    b.HasIndex("CreatedAt");

                    b.HasIndex("DepartmentId");

                    b.HasIndex("Priority");

                    b.HasIndex("RiskLevel");

                    b.HasIndex("ScheduledDate");

                    b.HasIndex("Status");

                    b.HasIndex("Type");

                    b.HasIndex("AuditorId", "Status");

                    b.HasIndex("Status", "ScheduledDate");

                    b.HasIndex("Type", "Status");

                    b.ToTable("Audits", (string)null);
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.Audits.AuditAttachment", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("AttachmentType")
                        .HasColumnType("integer");

                    b.Property<int>("AuditId")
                        .HasColumnType("integer");

                    b.Property<int?>("AuditItemId")
                        .HasColumnType("integer");

                    b.Property<string>("Category")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("ContentType")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(450)
                        .HasColumnType("character varying(450)");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("FileName")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("FilePath")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<long>("FileSize")
                        .HasColumnType("bigint");

                    b.Property<bool>("IsEvidence")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(450)
                        .HasColumnType("character varying(450)");

                    b.Property<string>("OriginalFileName")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<DateTime>("UploadedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UploadedBy")
                        .IsRequired()
                        .HasMaxLength(450)
                        .HasColumnType("character varying(450)");

                    b.HasKey("Id");

                    b.HasIndex("AttachmentType");

                    b.HasIndex("AuditId");

                    b.HasIndex("AuditItemId");

                    b.HasIndex("ContentType");

                    b.HasIndex("FileName");

                    b.HasIndex("IsEvidence");

                    b.HasIndex("UploadedAt");

                    b.HasIndex("AuditId", "Category");

                    b.HasIndex("AuditId", "IsEvidence");

                    b.ToTable("AuditAttachments", (string)null);
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.Audits.AuditComment", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int?>("AuditFindingId")
                        .HasColumnType("integer");

                    b.Property<int>("AuditId")
                        .HasColumnType("integer");

                    b.Property<int?>("AuditItemId")
                        .HasColumnType("integer");

                    b.Property<string>("Category")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Comment")
                        .IsRequired()
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<DateTime>("CommentedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CommentedBy")
                        .IsRequired()
                        .HasMaxLength(450)
                        .HasColumnType("character varying(450)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(450)
                        .HasColumnType("character varying(450)");

                    b.Property<bool>("IsInternal")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(450)
                        .HasColumnType("character varying(450)");

                    b.HasKey("Id");

                    b.HasIndex("AuditFindingId");

                    b.HasIndex("AuditId");

                    b.HasIndex("AuditItemId");

                    b.HasIndex("CommentedAt");

                    b.HasIndex("IsInternal");

                    b.HasIndex("AuditId", "Category");

                    b.HasIndex("AuditId", "CommentedAt");

                    b.ToTable("AuditComments", (string)null);
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.Audits.AuditFinding", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<decimal?>("ActualCost")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("AuditId")
                        .HasColumnType("integer");

                    b.Property<int?>("AuditItemId")
                        .HasColumnType("integer");

                    b.Property<string>("BusinessImpact")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<string>("ClosedBy")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<DateTime?>("ClosedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ClosureNotes")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<string>("CorrectiveAction")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(450)
                        .HasColumnType("character varying(450)");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<DateTime?>("DueDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Equipment")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<decimal?>("EstimatedCost")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("FindingNumber")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("ImmediateAction")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(450)
                        .HasColumnType("character varying(450)");

                    b.Property<string>("Location")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("PreventiveAction")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<string>("Regulation")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<bool>("RequiresVerification")
                        .HasColumnType("boolean");

                    b.Property<int?>("ResponsiblePersonId")
                        .HasColumnType("integer");

                    b.Property<string>("ResponsiblePersonName")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<int>("RiskLevel")
                        .HasColumnType("integer");

                    b.Property<string>("RootCause")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<int>("Severity")
                        .HasColumnType("integer");

                    b.Property<string>("Standard")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<int>("Type")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("VerificationDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("VerificationMethod")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("VerifiedBy")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.HasKey("Id");

                    b.HasIndex("AuditId");

                    b.HasIndex("AuditItemId");

                    b.HasIndex("DueDate");

                    b.HasIndex("FindingNumber")
                        .IsUnique();

                    b.HasIndex("RequiresVerification");

                    b.HasIndex("ResponsiblePersonId");

                    b.HasIndex("RiskLevel");

                    b.HasIndex("Severity");

                    b.HasIndex("Status");

                    b.HasIndex("Type");

                    b.HasIndex("VerificationDate");

                    b.HasIndex("AuditId", "Status");

                    b.HasIndex("Status", "Severity");

                    b.HasIndex("Type", "Severity");

                    b.ToTable("AuditFindings", (string)null);
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.Audits.AuditItem", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("AcceptanceCriteria")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<int?>("ActualPoints")
                        .HasColumnType("integer");

                    b.Property<string>("ActualResult")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<DateTime?>("AssessedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("AssessedBy")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<int>("AuditId")
                        .HasColumnType("integer");

                    b.Property<string>("Category")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Comments")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<string>("CorrectiveAction")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(450)
                        .HasColumnType("character varying(450)");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<DateTime?>("DueDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Evidence")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<string>("ExpectedResult")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<bool?>("IsCompliant")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsRequired")
                        .HasColumnType("boolean");

                    b.Property<string>("ItemNumber")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(450)
                        .HasColumnType("character varying(450)");

                    b.Property<int?>("MaxPoints")
                        .HasColumnType("integer");

                    b.Property<int?>("ResponsiblePersonId")
                        .HasColumnType("integer");

                    b.Property<int>("SortOrder")
                        .HasColumnType("integer");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<int>("Type")
                        .HasColumnType("integer");

                    b.Property<string>("ValidationCriteria")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.HasKey("Id");

                    b.HasIndex("AuditId");

                    b.HasIndex("Category");

                    b.HasIndex("DueDate");

                    b.HasIndex("IsRequired");

                    b.HasIndex("ItemNumber");

                    b.HasIndex("ResponsiblePersonId");

                    b.HasIndex("SortOrder");

                    b.HasIndex("Status");

                    b.HasIndex("Type");

                    b.HasIndex("AuditId", "SortOrder");

                    b.HasIndex("AuditId", "Status");

                    b.HasIndex("Status", "IsRequired");

                    b.ToTable("AuditItems", (string)null);
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.Audits.FindingAttachment", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("AttachmentType")
                        .HasColumnType("integer");

                    b.Property<int>("AuditFindingId")
                        .HasColumnType("integer");

                    b.Property<string>("ContentType")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(450)
                        .HasColumnType("character varying(450)");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("FileName")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("FilePath")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<long>("FileSize")
                        .HasColumnType("bigint");

                    b.Property<bool>("IsEvidence")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(450)
                        .HasColumnType("character varying(450)");

                    b.Property<string>("OriginalFileName")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<DateTime>("UploadedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UploadedBy")
                        .IsRequired()
                        .HasMaxLength(450)
                        .HasColumnType("character varying(450)");

                    b.HasKey("Id");

                    b.HasIndex("AttachmentType");

                    b.HasIndex("AuditFindingId");

                    b.HasIndex("ContentType");

                    b.HasIndex("FileName");

                    b.HasIndex("IsEvidence");

                    b.HasIndex("UploadedAt");

                    b.HasIndex("AuditFindingId", "AttachmentType");

                    b.HasIndex("AuditFindingId", "IsEvidence");

                    b.ToTable("AuditFindingAttachments", (string)null);
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.CorrectiveAction", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("AssignedToDepartment")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<int?>("AssignedToId")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("CompletedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CompletionNotes")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<int?>("DepartmentId")
                        .HasColumnType("integer");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<DateTime>("DueDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("IncidentId")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("Priority")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("AssignedToId");

                    b.HasIndex("DepartmentId");

                    b.HasIndex("DueDate");

                    b.HasIndex("IncidentId");

                    b.HasIndex("Status");

                    b.ToTable("CorrectiveActions", (string)null);
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.Department", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<string>("Contact")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<int>("DisplayOrder")
                        .HasColumnType("integer");

                    b.Property<string>("HeadOfDepartment")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Location")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.HasKey("Id");

                    b.HasIndex("Code")
                        .IsUnique();

                    b.HasIndex("IsActive");

                    b.HasIndex("Name");

                    b.ToTable("Departments", (string)null);
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.EmergencyContact", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Address")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<bool>("AuthorizedForMedicalDecisions")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<bool>("AuthorizedForPickup")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<int>("ContactOrder")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(1);

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("CustomRelationship")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Email")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<int>("HealthRecordId")
                        .HasColumnType("integer");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true);

                    b.Property<bool>("IsPrimaryContact")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("Notes")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("PrimaryPhone")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<int>("Relationship")
                        .HasColumnType("integer");

                    b.Property<string>("SecondaryPhone")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.HasKey("Id");

                    b.HasIndex("AuthorizedForMedicalDecisions");

                    b.HasIndex("AuthorizedForPickup");

                    b.HasIndex("CreatedAt");

                    b.HasIndex("HealthRecordId");

                    b.HasIndex("IsActive");

                    b.HasIndex("IsPrimaryContact");

                    b.HasIndex("Relationship");

                    b.HasIndex("HealthRecordId", "ContactOrder", "IsActive");

                    b.ToTable("EmergencyContacts", (string)null);
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.EscalationAction", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Channels")
                        .IsRequired()
                        .HasColumnType("json");

                    b.Property<double?>("Delay")
                        .HasColumnType("double precision");

                    b.Property<int>("EscalationRuleId")
                        .HasColumnType("integer");

                    b.Property<string>("Parameters")
                        .IsRequired()
                        .HasColumnType("json");

                    b.Property<string>("Target")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("TemplateId")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<int>("Type")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("EscalationRuleId");

                    b.HasIndex("Type");

                    b.ToTable("EscalationActions");
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.EscalationHistory", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("ActionDetails")
                        .IsRequired()
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<string>("ActionTarget")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<int>("ActionType")
                        .HasColumnType("integer");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("ErrorMessage")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<int?>("EscalationRuleId")
                        .HasColumnType("integer");

                    b.Property<DateTime>("ExecutedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ExecutedBy")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<int>("IncidentId")
                        .HasColumnType("integer");

                    b.Property<bool>("IsSuccessful")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("RuleName")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.HasKey("Id");

                    b.HasIndex("EscalationRuleId");

                    b.HasIndex("ExecutedAt");

                    b.HasIndex("IncidentId");

                    b.HasIndex("IsSuccessful");

                    b.ToTable("EscalationHistories");
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.EscalationRule", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true);

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<int>("Priority")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(100);

                    b.Property<double?>("TriggerAfterDuration")
                        .HasColumnType("double precision");

                    b.Property<string>("TriggerDepartments")
                        .IsRequired()
                        .HasColumnType("json");

                    b.Property<string>("TriggerLocations")
                        .IsRequired()
                        .HasColumnType("json");

                    b.Property<string>("TriggerSeverities")
                        .IsRequired()
                        .HasColumnType("json");

                    b.Property<string>("TriggerStatuses")
                        .IsRequired()
                        .HasColumnType("json");

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt");

                    b.HasIndex("IsActive");

                    b.HasIndex("Priority");

                    b.ToTable("EscalationRules");
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.Hazard", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int?>("CategoryId")
                        .HasColumnType("integer");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<int?>("CurrentRiskAssessmentId")
                        .HasColumnType("integer");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<DateTime?>("ExpectedResolutionDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("IdentifiedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("Location")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("ReporterDepartment")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<int>("ReporterId")
                        .HasColumnType("integer");

                    b.Property<string>("Severity")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<int?>("TypeId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("CategoryId");

                    b.HasIndex("CreatedAt");

                    b.HasIndex("CurrentRiskAssessmentId")
                        .IsUnique();

                    b.HasIndex("IdentifiedDate");

                    b.HasIndex("ReporterDepartment");

                    b.HasIndex("ReporterId");

                    b.HasIndex("Severity");

                    b.HasIndex("Status");

                    b.HasIndex("TypeId");

                    b.HasIndex("CategoryId", "Status");

                    b.HasIndex("Status", "Severity");

                    b.ToTable("Hazards");
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.HazardAttachment", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("ContentType")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("FileName")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("FilePath")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<long>("FileSize")
                        .HasColumnType("bigint")
                        .HasComment("File size in bytes");

                    b.Property<int>("HazardId")
                        .HasColumnType("integer");

                    b.Property<DateTime>("UploadedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UploadedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.HasKey("Id");

                    b.HasIndex("ContentType");

                    b.HasIndex("FileName");

                    b.HasIndex("HazardId");

                    b.HasIndex("UploadedAt");

                    b.ToTable("HazardAttachments");
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.HazardAuditLog", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Action")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("ChangeDescription")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<DateTime>("ChangedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ChangedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("FieldName")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<int>("HazardId")
                        .HasColumnType("integer");

                    b.Property<string>("IpAddress")
                        .HasMaxLength(45)
                        .HasColumnType("character varying(45)");

                    b.Property<string>("NewValue")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("OldValue")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("UserAgent")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.HasKey("Id");

                    b.HasIndex("HazardId", "ChangedAt")
                        .HasDatabaseName("IX_HazardAuditLogs_HazardId_ChangedAt");

                    b.ToTable("HazardAuditLogs", (string)null);
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.HazardCategory", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<string>("Color")
                        .IsRequired()
                        .HasMaxLength(7)
                        .HasColumnType("character varying(7)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<int>("DisplayOrder")
                        .HasColumnType("integer");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("RiskLevel")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.HasKey("Id");

                    b.HasIndex("Code")
                        .IsUnique();

                    b.HasIndex("IsActive");

                    b.HasIndex("Name");

                    b.HasIndex("RiskLevel");

                    b.ToTable("HazardCategories", (string)null);
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.HazardMitigationAction", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("ActionDescription")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<decimal?>("ActualCost")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("AssignedToId")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("CompletedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CompletionNotes")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("EffectivenessNotes")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<int?>("EffectivenessRating")
                        .HasColumnType("integer")
                        .HasComment("Effectiveness rating (1-5)");

                    b.Property<decimal?>("EstimatedCost")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("HazardId")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("Priority")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool>("RequiresVerification")
                        .HasColumnType("boolean");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("TargetDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("VerificationNotes")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<DateTime?>("VerifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int?>("VerifiedById")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("AssignedToId");

                    b.HasIndex("HazardId");

                    b.HasIndex("Priority");

                    b.HasIndex("RequiresVerification");

                    b.HasIndex("Status");

                    b.HasIndex("TargetDate");

                    b.HasIndex("Type");

                    b.HasIndex("VerifiedById");

                    b.HasIndex("AssignedToId", "Status");

                    b.HasIndex("HazardId", "Status");

                    b.HasIndex("Status", "TargetDate");

                    b.ToTable("HazardMitigationActions");
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.HazardReassessment", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime?>("CompletedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int?>("CompletedById")
                        .HasColumnType("integer");

                    b.Property<string>("CompletionNotes")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("HazardId")
                        .HasColumnType("integer");

                    b.Property<bool>("IsCompleted")
                        .HasColumnType("boolean");

                    b.Property<string>("Reason")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<DateTime>("ScheduledDate")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("CompletedAt");

                    b.HasIndex("CompletedById");

                    b.HasIndex("HazardId");

                    b.HasIndex("IsCompleted");

                    b.HasIndex("ScheduledDate");

                    b.HasIndex("IsCompleted", "ScheduledDate");

                    b.ToTable("HazardReassessments");
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.HazardType", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int?>("CategoryId")
                        .HasColumnType("integer");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<int>("DisplayOrder")
                        .HasColumnType("integer");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<bool>("RequiresPermit")
                        .HasColumnType("boolean");

                    b.Property<decimal>("RiskMultiplier")
                        .HasPrecision(5, 2)
                        .HasColumnType("numeric(5,2)");

                    b.HasKey("Id");

                    b.HasIndex("CategoryId");

                    b.HasIndex("Code")
                        .IsUnique();

                    b.HasIndex("IsActive");

                    b.HasIndex("Name");

                    b.HasIndex("RequiresPermit");

                    b.ToTable("HazardTypes", (string)null);
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.HealthIncident", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("FollowUpRequired")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<int>("HealthRecordId")
                        .HasColumnType("integer");

                    b.Property<DateTime>("IncidentDateTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int?>("IncidentId")
                        .HasColumnType("integer");

                    b.Property<bool>("IsResolved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime?>("ParentNotificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("ParentsNotified")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<bool>("RequiredHospitalization")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<string>("ResolutionNotes")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<DateTime?>("ReturnToSchoolDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("Severity")
                        .HasColumnType("integer");

                    b.Property<string>("Symptoms")
                        .IsRequired()
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<string>("TreatedBy")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<int>("TreatmentLocation")
                        .HasColumnType("integer");

                    b.Property<string>("TreatmentProvided")
                        .IsRequired()
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<int>("Type")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt");

                    b.HasIndex("HealthRecordId");

                    b.HasIndex("IncidentDateTime");

                    b.HasIndex("IncidentId");

                    b.HasIndex("IsResolved");

                    b.HasIndex("RequiredHospitalization");

                    b.HasIndex("Severity");

                    b.HasIndex("Type");

                    b.HasIndex("Type", "Severity", "IncidentDateTime");

                    b.ToTable("HealthIncidents", (string)null);
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.HealthRecord", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int?>("BloodType")
                        .HasColumnType("integer");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime?>("DateOfBirth")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true);

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("MedicalNotes")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<int>("PersonId")
                        .HasColumnType("integer");

                    b.Property<int>("PersonType")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt");

                    b.HasIndex("IsActive");

                    b.HasIndex("PersonId")
                        .IsUnique();

                    b.HasIndex("PersonType");

                    b.ToTable("HealthRecords", (string)null);
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.Incident", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int?>("CategoryId")
                        .HasColumnType("integer");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<int?>("DepartmentId")
                        .HasColumnType("integer");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<bool>("EmergencyServicesContacted")
                        .HasColumnType("boolean");

                    b.Property<string>("ImmediateActionsTaken")
                        .HasColumnType("text");

                    b.Property<DateTime>("IncidentDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int?>("InjuryType")
                        .HasColumnType("integer");

                    b.Property<int?>("InvestigatorId")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<DateTime?>("LastResponseAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Location")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<int?>("LocationId")
                        .HasColumnType("integer");

                    b.Property<bool>("MedicalTreatmentProvided")
                        .HasColumnType("boolean");

                    b.Property<string>("ReporterDepartment")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("ReporterEmail")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int?>("ReporterId")
                        .HasColumnType("integer");

                    b.Property<string>("ReporterName")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Severity")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("WitnessNames")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("CategoryId");

                    b.HasIndex("CreatedAt");

                    b.HasIndex("DepartmentId");

                    b.HasIndex("IncidentDate");

                    b.HasIndex("InvestigatorId");

                    b.HasIndex("LocationId");

                    b.HasIndex("ReporterId");

                    b.HasIndex("Severity");

                    b.HasIndex("Status");

                    b.ToTable("Incidents");
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.IncidentAttachment", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("FileName")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("FilePath")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<long>("FileSize")
                        .HasColumnType("bigint");

                    b.Property<int>("IncidentId")
                        .HasColumnType("integer");

                    b.Property<DateTime>("UploadedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UploadedBy")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("IncidentId");

                    b.ToTable("IncidentAttachments");
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.IncidentAuditLog", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Action")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("ChangeDescription")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<DateTime>("ChangedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ChangedBy")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("FieldName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<int>("IncidentId")
                        .HasColumnType("integer");

                    b.Property<string>("NewValue")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<string>("OldValue")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.HasKey("Id");

                    b.HasIndex("ChangedAt");

                    b.HasIndex("IncidentId");

                    b.HasIndex("IncidentId", "ChangedAt");

                    b.ToTable("IncidentAuditLogs", (string)null);
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.IncidentCategory", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<string>("Color")
                        .IsRequired()
                        .HasMaxLength(7)
                        .HasColumnType("character varying(7)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<int>("DisplayOrder")
                        .HasColumnType("integer");

                    b.Property<string>("Icon")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<bool>("RequiresImmediateAction")
                        .HasColumnType("boolean");

                    b.HasKey("Id");

                    b.HasIndex("Code")
                        .IsUnique();

                    b.HasIndex("IsActive");

                    b.HasIndex("Name");

                    b.ToTable("IncidentCategories", (string)null);
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.IncidentInvolvedPerson", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("IncidentId")
                        .HasColumnType("integer");

                    b.Property<string>("InjuryDescription")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<int>("InvolvementType")
                        .HasColumnType("integer");

                    b.Property<string>("ManualPersonEmail")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("ManualPersonName")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<int?>("PersonId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("PersonId");

                    b.HasIndex("IncidentId", "ManualPersonName")
                        .HasDatabaseName("IX_IncidentInvolvedPersons_IncidentId_ManualPersonName");

                    b.HasIndex("IncidentId", "PersonId")
                        .IsUnique()
                        .HasDatabaseName("IX_IncidentInvolvedPersons_IncidentId_PersonId")
                        .HasFilter("[PersonId] IS NOT NULL");

                    b.ToTable("IncidentInvolvedPersons", (string)null);
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.IncidentLocation", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Building")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<int>("DisplayOrder")
                        .HasColumnType("integer");

                    b.Property<string>("Floor")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsHighRisk")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("Room")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.HasKey("Id");

                    b.HasIndex("Building");

                    b.HasIndex("Code")
                        .IsUnique();

                    b.HasIndex("IsActive");

                    b.HasIndex("Name");

                    b.ToTable("IncidentLocations", (string)null);
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.Inspections.FindingAttachment", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("ContentType")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(450)
                        .HasColumnType("character varying(450)");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("FileName")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("FilePath")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<long>("FileSize")
                        .HasColumnType("bigint");

                    b.Property<int>("FindingId")
                        .HasColumnType("integer");

                    b.Property<bool>("IsPhoto")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(450)
                        .HasColumnType("character varying(450)");

                    b.Property<string>("OriginalFileName")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("ThumbnailPath")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt");

                    b.HasIndex("FindingId");

                    b.HasIndex("IsPhoto");

                    b.ToTable("FindingAttachments", (string)null);
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.Inspections.Inspection", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int?>("ActualDurationMinutes")
                        .HasColumnType("integer");

                    b.Property<int>("Category")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("CompletedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(450)
                        .HasColumnType("character varying(450)");

                    b.Property<int?>("DepartmentId")
                        .HasColumnType("integer");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<int?>("EstimatedDurationMinutes")
                        .HasColumnType("integer");

                    b.Property<int?>("FacilityId")
                        .HasColumnType("integer");

                    b.Property<string>("InspectionNumber")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<int>("InspectorId")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(450)
                        .HasColumnType("character varying(450)");

                    b.Property<int?>("LocationId")
                        .HasColumnType("integer");

                    b.Property<int>("Priority")
                        .HasColumnType("integer");

                    b.Property<string>("Recommendations")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<int>("RiskLevel")
                        .HasColumnType("integer");

                    b.Property<DateTime>("ScheduledDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("StartedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<string>("Summary")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<int>("Type")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt");

                    b.HasIndex("DepartmentId");

                    b.HasIndex("InspectionNumber")
                        .IsUnique();

                    b.HasIndex("InspectorId");

                    b.HasIndex("Priority");

                    b.HasIndex("ScheduledDate");

                    b.HasIndex("Status");

                    b.HasIndex("Type");

                    b.HasIndex("InspectorId", "Status");

                    b.HasIndex("Status", "ScheduledDate");

                    b.ToTable("Inspections", (string)null);
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.Inspections.InspectionAttachment", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Category")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("ContentType")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(450)
                        .HasColumnType("character varying(450)");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("FileName")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("FilePath")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<long>("FileSize")
                        .HasColumnType("bigint");

                    b.Property<int>("InspectionId")
                        .HasColumnType("integer");

                    b.Property<bool>("IsPhoto")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(450)
                        .HasColumnType("character varying(450)");

                    b.Property<string>("OriginalFileName")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("ThumbnailPath")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.HasKey("Id");

                    b.HasIndex("Category");

                    b.HasIndex("CreatedAt");

                    b.HasIndex("InspectionId");

                    b.HasIndex("IsPhoto");

                    b.ToTable("InspectionAttachments", (string)null);
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.Inspections.InspectionComment", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Comment")
                        .IsRequired()
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(450)
                        .HasColumnType("character varying(450)");

                    b.Property<int>("InspectionId")
                        .HasColumnType("integer");

                    b.Property<bool>("IsInternal")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(450)
                        .HasColumnType("character varying(450)");

                    b.Property<int?>("ParentCommentId")
                        .HasColumnType("integer");

                    b.Property<int>("UserId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt");

                    b.HasIndex("InspectionId");

                    b.HasIndex("ParentCommentId");

                    b.HasIndex("UserId");

                    b.HasIndex("InspectionId", "CreatedAt");

                    b.ToTable("InspectionComments", (string)null);
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.Inspections.InspectionFinding", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime?>("ClosedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ClosureNotes")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<string>("CorrectiveAction")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(450)
                        .HasColumnType("character varying(450)");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<DateTime?>("DueDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Equipment")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("FindingNumber")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("ImmediateAction")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<int>("InspectionId")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(450)
                        .HasColumnType("character varying(450)");

                    b.Property<string>("Location")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("Regulation")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<int?>("ResponsiblePersonId")
                        .HasColumnType("integer");

                    b.Property<int>("RiskLevel")
                        .HasColumnType("integer");

                    b.Property<string>("RootCause")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<int>("Severity")
                        .HasColumnType("integer");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<int>("Type")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("DueDate");

                    b.HasIndex("FindingNumber")
                        .IsUnique();

                    b.HasIndex("InspectionId");

                    b.HasIndex("ResponsiblePersonId");

                    b.HasIndex("Severity");

                    b.HasIndex("Status");

                    b.HasIndex("Type");

                    b.HasIndex("Status", "DueDate");

                    b.ToTable("InspectionFindings", (string)null);
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.Inspections.InspectionItem", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int?>("ChecklistItemId")
                        .HasColumnType("integer");

                    b.Property<string>("Description")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<string>("ExpectedValue")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<int>("InspectionId")
                        .HasColumnType("integer");

                    b.Property<bool>("IsRequired")
                        .HasColumnType("boolean");

                    b.Property<decimal?>("MaxValue")
                        .HasPrecision(18, 4)
                        .HasColumnType("numeric(18,4)");

                    b.Property<decimal?>("MinValue")
                        .HasPrecision(18, 4)
                        .HasColumnType("numeric(18,4)");

                    b.Property<string>("Notes")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<string>("Options")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<string>("Question")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("Response")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<int>("SortOrder")
                        .HasColumnType("integer");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<int>("Type")
                        .HasColumnType("integer");

                    b.Property<string>("Unit")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.HasKey("Id");

                    b.HasIndex("InspectionId");

                    b.HasIndex("Status");

                    b.HasIndex("InspectionId", "SortOrder");

                    b.ToTable("InspectionItems", (string)null);
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.MedicalCondition", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<DateTime?>("DiagnosedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("EmergencyInstructions")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<int>("HealthRecordId")
                        .HasColumnType("integer");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true);

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<bool>("RequiresEmergencyAction")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<int>("Severity")
                        .HasColumnType("integer");

                    b.Property<string>("TreatmentPlan")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<int>("Type")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt");

                    b.HasIndex("HealthRecordId");

                    b.HasIndex("IsActive");

                    b.HasIndex("RequiresEmergencyAction");

                    b.HasIndex("Severity");

                    b.HasIndex("Type");

                    b.ToTable("MedicalConditions", (string)null);
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.ModulePermission", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true);

                    b.Property<int>("Module")
                        .HasColumnType("integer");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<int>("Permission")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("IsActive")
                        .HasDatabaseName("IX_ModulePermission_IsActive");

                    b.HasIndex("Module")
                        .HasDatabaseName("IX_ModulePermission_Module");

                    b.HasIndex("Module", "Permission")
                        .IsUnique()
                        .HasDatabaseName("IX_ModulePermission_Module_Permission");

                    b.ToTable("ModulePermissions", (string)null);
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.NotificationHistory", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("Channel")
                        .HasColumnType("integer");

                    b.Property<string>("Content")
                        .IsRequired()
                        .HasMaxLength(5000)
                        .HasColumnType("character varying(5000)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime?>("DeliveredAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ErrorMessage")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<int>("IncidentId")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Metadata")
                        .IsRequired()
                        .HasColumnType("json");

                    b.Property<int>("Priority")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("ReadAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("RecipientId")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("RecipientType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime?>("SentAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<string>("Subject")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("TemplateId")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.HasKey("Id");

                    b.HasIndex("Channel");

                    b.HasIndex("CreatedAt");

                    b.HasIndex("IncidentId");

                    b.HasIndex("RecipientId");

                    b.HasIndex("Status");

                    b.HasIndex("IncidentId", "RecipientId");

                    b.ToTable("NotificationHistories");
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.PPEAssignment", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("AssignedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<DateTime>("AssignedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("AssignedToId")
                        .HasColumnType("integer");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<int>("PPEItemId")
                        .HasColumnType("integer");

                    b.Property<string>("Purpose")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("ReturnNotes")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<string>("ReturnedBy")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<DateTime?>("ReturnedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("AssignedDate");

                    b.HasIndex("AssignedToId");

                    b.HasIndex("PPEItemId");

                    b.HasIndex("ReturnedDate");

                    b.HasIndex("Status");

                    b.HasIndex("PPEItemId", "Status");

                    b.ToTable("PPEAssignments");
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.PPECategory", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<string>("ComplianceStandard")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<int?>("DefaultExpiryDays")
                        .HasColumnType("integer");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<int?>("InspectionIntervalDays")
                        .HasColumnType("integer");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true);

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<bool>("RequiresCertification")
                        .HasColumnType("boolean");

                    b.Property<bool>("RequiresExpiry")
                        .HasColumnType("boolean");

                    b.Property<bool>("RequiresInspection")
                        .HasColumnType("boolean");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("Code")
                        .IsUnique()
                        .HasDatabaseName("IX_PPECategories_Code");

                    b.HasIndex("IsActive")
                        .HasDatabaseName("IX_PPECategories_IsActive");

                    b.HasIndex("Name")
                        .HasDatabaseName("IX_PPECategories_Name");

                    b.HasIndex("Type")
                        .HasDatabaseName("IX_PPECategories_Type");

                    b.ToTable("PPECategories", (string)null);
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.PPEComplianceRequirement", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("CategoryId")
                        .HasColumnType("integer");

                    b.Property<string>("ComplianceNote")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsMandatory")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<int?>("MinimumQuantity")
                        .HasColumnType("integer");

                    b.Property<int?>("ReplacementIntervalDays")
                        .HasColumnType("integer");

                    b.Property<bool>("RequiresTraining")
                        .HasColumnType("boolean");

                    b.Property<string>("RiskAssessmentReference")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<int>("RoleId")
                        .HasColumnType("integer");

                    b.Property<string>("TrainingRequirements")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.HasKey("Id");

                    b.HasIndex("CategoryId");

                    b.HasIndex("IsActive");

                    b.HasIndex("IsMandatory");

                    b.HasIndex("RoleId");

                    b.HasIndex("RoleId", "CategoryId")
                        .IsUnique();

                    b.ToTable("PPEComplianceRequirements");
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.PPEInspection", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("CorrectiveActions")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("Findings")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<DateTime>("InspectionDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("InspectorId")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("MaintenanceNotes")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<DateTime?>("NextInspectionDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("PPEItemId")
                        .HasColumnType("integer");

                    b.Property<string>("PhotoPathsJson")
                        .IsRequired()
                        .HasMaxLength(4000)
                        .HasColumnType("character varying(4000)")
                        .HasColumnName("PhotoPaths");

                    b.Property<string>("RecommendedCondition")
                        .HasColumnType("text");

                    b.Property<bool>("RequiresMaintenance")
                        .HasColumnType("boolean");

                    b.Property<string>("Result")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("InspectionDate");

                    b.HasIndex("InspectorId");

                    b.HasIndex("NextInspectionDate");

                    b.HasIndex("PPEItemId");

                    b.HasIndex("Result");

                    b.HasIndex("PPEItemId", "InspectionDate");

                    b.ToTable("PPEInspections");
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.PPEItem", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime?>("AssignedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int?>("AssignedToId")
                        .HasColumnType("integer");

                    b.Property<int>("CategoryId")
                        .HasColumnType("integer");

                    b.Property<string>("Color")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("Condition")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<decimal>("Cost")
                        .HasPrecision(18, 2)
                        .HasColumnType("numeric(18,2)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<DateTime?>("ExpiryDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ItemCode")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("Location")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("Manufacturer")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Model")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("Notes")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<DateTime>("PurchaseDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int?>("SizeId")
                        .HasColumnType("integer");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int?>("StorageLocationId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("AssignedToId");

                    b.HasIndex("CategoryId");

                    b.HasIndex("Condition");

                    b.HasIndex("CreatedAt");

                    b.HasIndex("ExpiryDate");

                    b.HasIndex("ItemCode")
                        .IsUnique();

                    b.HasIndex("SizeId");

                    b.HasIndex("Status");

                    b.HasIndex("StorageLocationId");

                    b.HasIndex("Status", "CategoryId");

                    b.ToTable("PPEItems");
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.PPERequest", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("ApprovedBy")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<DateTime?>("ApprovedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("CategoryId")
                        .HasColumnType("integer");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("FulfilledBy")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<DateTime?>("FulfilledDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int?>("FulfilledPPEItemId")
                        .HasColumnType("integer");

                    b.Property<string>("Justification")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("Notes")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<string>("Priority")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("RejectionReason")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<DateTime>("RequestDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("RequestNumber")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<int>("RequesterId")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("RequiredDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("ReviewedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int?>("ReviewerId")
                        .HasColumnType("integer");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("CategoryId");

                    b.HasIndex("FulfilledPPEItemId");

                    b.HasIndex("Priority");

                    b.HasIndex("RequestDate");

                    b.HasIndex("RequestNumber")
                        .IsUnique();

                    b.HasIndex("RequesterId");

                    b.HasIndex("RequiredDate");

                    b.HasIndex("ReviewerId");

                    b.HasIndex("Status");

                    b.HasIndex("Status", "Priority");

                    b.ToTable("PPERequests");
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.PPERequestItem", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("ItemDescription")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<int>("Quantity")
                        .HasColumnType("integer");

                    b.Property<int>("RequestId")
                        .HasColumnType("integer");

                    b.Property<string>("Size")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("SpecialRequirements")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.HasKey("Id");

                    b.HasIndex("RequestId");

                    b.ToTable("PPERequestItems");
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.PPESize", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true);

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<int>("SortOrder")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0);

                    b.HasKey("Id");

                    b.HasIndex("Code")
                        .IsUnique()
                        .HasDatabaseName("IX_PPESizes_Code");

                    b.HasIndex("IsActive")
                        .HasDatabaseName("IX_PPESizes_IsActive");

                    b.HasIndex("Name")
                        .HasDatabaseName("IX_PPESizes_Name");

                    b.HasIndex("SortOrder")
                        .HasDatabaseName("IX_PPESizes_SortOrder");

                    b.ToTable("PPESizes", (string)null);
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.PPEStorageLocation", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Address")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<int>("Capacity")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(1000);

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("ContactPerson")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("ContactPhone")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<int>("CurrentStock")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0);

                    b.Property<string>("Description")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true);

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.HasKey("Id");

                    b.HasIndex("Code")
                        .IsUnique()
                        .HasDatabaseName("IX_PPEStorageLocations_Code");

                    b.HasIndex("IsActive")
                        .HasDatabaseName("IX_PPEStorageLocations_IsActive");

                    b.HasIndex("Name")
                        .HasDatabaseName("IX_PPEStorageLocations_Name");

                    b.ToTable("PPEStorageLocations", (string)null);
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.Permission", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Category")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("DisplayName")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.HasKey("Id");

                    b.HasIndex("Name")
                        .IsUnique();

                    b.ToTable("Permissions", (string)null);
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.RiskAssessment", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("AdditionalNotes")
                        .IsRequired()
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<string>("ApprovalNotes")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<DateTime?>("ApprovedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int?>("ApprovedById")
                        .HasColumnType("integer");

                    b.Property<DateTime>("AssessmentDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("AssessorId")
                        .HasColumnType("integer");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("ExistingControls")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<int>("HazardId")
                        .HasColumnType("integer");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsApproved")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<DateTime>("NextReviewDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("PotentialConsequences")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<int>("ProbabilityScore")
                        .HasColumnType("integer")
                        .HasComment("Risk probability score (1-5)");

                    b.Property<string>("RecommendedActions")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<string>("RiskLevel")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("RiskScore")
                        .HasColumnType("integer")
                        .HasComment("Calculated risk score (Probability × Severity)");

                    b.Property<int>("SeverityScore")
                        .HasColumnType("integer")
                        .HasComment("Risk severity score (1-5)");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("ApprovedById");

                    b.HasIndex("AssessmentDate");

                    b.HasIndex("AssessorId");

                    b.HasIndex("HazardId");

                    b.HasIndex("IsActive");

                    b.HasIndex("IsApproved");

                    b.HasIndex("NextReviewDate");

                    b.HasIndex("RiskLevel");

                    b.HasIndex("RiskScore");

                    b.HasIndex("Type");

                    b.HasIndex("HazardId", "IsActive");

                    b.HasIndex("RiskLevel", "IsActive");

                    b.ToTable("RiskAssessments");
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.Role", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<int>("DisplayOrder")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0);

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true);

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<int>("RoleType")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("IsActive");

                    b.HasIndex("Name")
                        .IsUnique();

                    b.HasIndex("RoleType")
                        .IsUnique();

                    b.ToTable("Roles");
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.RoleModulePermission", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("GrantReason")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<DateTime>("GrantedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<int?>("GrantedByUserId")
                        .HasColumnType("integer");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true);

                    b.Property<int>("ModulePermissionId")
                        .HasColumnType("integer");

                    b.Property<int>("RoleId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("GrantedByUserId")
                        .HasDatabaseName("IX_RoleModulePermission_GrantedByUserId");

                    b.HasIndex("IsActive")
                        .HasDatabaseName("IX_RoleModulePermission_IsActive");

                    b.HasIndex("ModulePermissionId")
                        .HasDatabaseName("IX_RoleModulePermission_ModulePermissionId");

                    b.HasIndex("RoleId")
                        .HasDatabaseName("IX_RoleModulePermission_RoleId");

                    b.HasIndex("RoleId", "ModulePermissionId")
                        .IsUnique()
                        .HasDatabaseName("IX_RoleModulePermission_Role_ModulePermission");

                    b.ToTable("RoleModulePermissions", (string)null);
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.Security.SecurityAuditLog", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("Action")
                        .HasColumnType("integer");

                    b.Property<DateTime>("ActionTimestamp")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("Category")
                        .HasColumnType("integer");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Details")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("IpAddress")
                        .IsRequired()
                        .HasMaxLength(45)
                        .HasColumnType("character varying(45)");

                    b.Property<bool>("IsSecurityCritical")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasColumnType("text");

                    b.Property<string>("Metadata")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int?>("RelatedIncidentId")
                        .HasColumnType("integer");

                    b.Property<string>("Resource")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("SessionId")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<int>("Severity")
                        .HasColumnType("integer");

                    b.Property<string>("UserAgent")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<int>("UserId")
                        .HasColumnType("integer");

                    b.Property<string>("UserName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("UserRole")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.HasKey("Id");

                    b.HasIndex("Action")
                        .HasDatabaseName("IX_SecurityAuditLogs_Action");

                    b.HasIndex("ActionTimestamp")
                        .HasDatabaseName("IX_SecurityAuditLogs_ActionTimestamp");

                    b.HasIndex("Category")
                        .HasDatabaseName("IX_SecurityAuditLogs_Category");

                    b.HasIndex("IpAddress")
                        .HasDatabaseName("IX_SecurityAuditLogs_IpAddress");

                    b.HasIndex("IsSecurityCritical")
                        .HasDatabaseName("IX_SecurityAuditLogs_IsSecurityCritical");

                    b.HasIndex("RelatedIncidentId")
                        .HasDatabaseName("IX_SecurityAuditLogs_RelatedIncidentId");

                    b.HasIndex("SessionId")
                        .HasDatabaseName("IX_SecurityAuditLogs_SessionId");

                    b.HasIndex("Severity")
                        .HasDatabaseName("IX_SecurityAuditLogs_Severity");

                    b.HasIndex("UserId")
                        .HasDatabaseName("IX_SecurityAuditLogs_UserId");

                    b.HasIndex("Action", "ActionTimestamp")
                        .HasDatabaseName("IX_SecurityAuditLogs_Action_Timestamp");

                    b.HasIndex("Category", "ActionTimestamp")
                        .HasDatabaseName("IX_SecurityAuditLogs_Category_Timestamp");

                    b.HasIndex("IsSecurityCritical", "ActionTimestamp")
                        .HasDatabaseName("IX_SecurityAuditLogs_Critical_Timestamp");

                    b.HasIndex("RelatedIncidentId", "ActionTimestamp")
                        .HasDatabaseName("IX_SecurityAuditLogs_Incident_Timestamp");

                    b.HasIndex("UserId", "ActionTimestamp")
                        .HasDatabaseName("IX_SecurityAuditLogs_User_Timestamp");

                    b.ToTable("SecurityAuditLogs", (string)null);
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.Security.SecurityControl", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<decimal?>("AnnualMaintenanceCost")
                        .HasColumnType("decimal(15,2)");

                    b.Property<int>("Category")
                        .HasColumnType("integer");

                    b.Property<string>("ControlDescription")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("ControlName")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<int>("ControlType")
                        .HasColumnType("integer");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("EffectivenessNotes")
                        .HasColumnType("text");

                    b.Property<int?>("EffectivenessScore")
                        .HasColumnType("integer")
                        .HasAnnotation("CheckConstraint", "EffectivenessScore BETWEEN 1 AND 10");

                    b.Property<decimal?>("ImplementationCost")
                        .HasColumnType("decimal(15,2)");

                    b.Property<DateTime>("ImplementationDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("ImplementedById")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasColumnType("text");

                    b.Property<DateTime?>("NextReviewDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int?>("RelatedIncidentId")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("ReviewDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int?>("ReviewedById")
                        .HasColumnType("integer");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("Category")
                        .HasDatabaseName("IX_SecurityControls_Category");

                    b.HasIndex("ControlType")
                        .HasDatabaseName("IX_SecurityControls_ControlType");

                    b.HasIndex("ImplementedById")
                        .HasDatabaseName("IX_SecurityControls_ImplementedById");

                    b.HasIndex("NextReviewDate")
                        .HasDatabaseName("IX_SecurityControls_NextReviewDate");

                    b.HasIndex("RelatedIncidentId")
                        .HasDatabaseName("IX_SecurityControls_RelatedIncidentId");

                    b.HasIndex("ReviewedById");

                    b.HasIndex("Status")
                        .HasDatabaseName("IX_SecurityControls_Status");

                    b.HasIndex("ControlType", "Category")
                        .HasDatabaseName("IX_SecurityControls_Type_Category");

                    b.HasIndex("Status", "NextReviewDate")
                        .HasDatabaseName("IX_SecurityControls_Status_NextReviewDate");

                    b.ToTable("SecurityControls", (string)null);
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.Security.SecurityIncident", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int?>("AffectedPersonsCount")
                        .HasColumnType("integer");

                    b.Property<int?>("AssignedToId")
                        .HasColumnType("integer");

                    b.Property<int>("Category")
                        .HasColumnType("integer");

                    b.Property<string>("ContainmentActions")
                        .HasColumnType("text");

                    b.Property<DateTime?>("ContainmentDateTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool>("DataBreachOccurred")
                        .HasColumnType("boolean");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("DetectionDateTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<decimal?>("EstimatedLoss")
                        .HasColumnType("decimal(15,2)");

                    b.Property<int>("Impact")
                        .HasColumnType("integer");

                    b.Property<DateTime>("IncidentDateTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("IncidentNumber")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<int>("IncidentType")
                        .HasColumnType("integer");

                    b.Property<int?>("InvestigatorId")
                        .HasColumnType("integer");

                    b.Property<bool>("IsInternalThreat")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasColumnType("text");

                    b.Property<string>("Location")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<int>("ReporterId")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("ResolutionDateTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("RootCause")
                        .HasColumnType("text");

                    b.Property<int>("Severity")
                        .HasColumnType("integer");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<string>("ThreatActorDescription")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<int?>("ThreatActorType")
                        .HasColumnType("integer");

                    b.Property<int>("ThreatLevel")
                        .HasColumnType("integer");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.HasKey("Id");

                    b.HasIndex("AssignedToId")
                        .HasDatabaseName("IX_SecurityIncidents_AssignedToId");

                    b.HasIndex("IncidentDateTime")
                        .HasDatabaseName("IX_SecurityIncidents_IncidentDateTime");

                    b.HasIndex("IncidentNumber")
                        .IsUnique();

                    b.HasIndex("IncidentType")
                        .HasDatabaseName("IX_SecurityIncidents_IncidentType");

                    b.HasIndex("InvestigatorId")
                        .HasDatabaseName("IX_SecurityIncidents_InvestigatorId");

                    b.HasIndex("ReporterId")
                        .HasDatabaseName("IX_SecurityIncidents_ReporterId");

                    b.HasIndex("Severity")
                        .HasDatabaseName("IX_SecurityIncidents_Severity");

                    b.HasIndex("Status")
                        .HasDatabaseName("IX_SecurityIncidents_Status");

                    b.HasIndex("ThreatLevel")
                        .HasDatabaseName("IX_SecurityIncidents_ThreatLevel");

                    b.HasIndex("IncidentType", "Status")
                        .HasDatabaseName("IX_SecurityIncidents_Type_Status");

                    b.HasIndex("Severity", "CreatedAt")
                        .HasDatabaseName("IX_SecurityIncidents_Severity_CreatedAt");

                    b.ToTable("SecurityIncidents", (string)null);
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.Security.SecurityIncidentAttachment", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("AttachmentType")
                        .HasColumnType("integer");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<string>("FileName")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("FilePath")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<long>("FileSize")
                        .HasColumnType("bigint");

                    b.Property<string>("FileType")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Hash")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.Property<bool>("IsConfidential")
                        .HasColumnType("boolean");

                    b.Property<int>("SecurityIncidentId")
                        .HasColumnType("integer");

                    b.Property<DateTime>("UploadedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UploadedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.HasKey("Id");

                    b.HasIndex("AttachmentType")
                        .HasDatabaseName("IX_SecurityIncidentAttachments_AttachmentType");

                    b.HasIndex("IsConfidential")
                        .HasDatabaseName("IX_SecurityIncidentAttachments_IsConfidential");

                    b.HasIndex("SecurityIncidentId")
                        .HasDatabaseName("IX_SecurityIncidentAttachments_SecurityIncidentId");

                    b.HasIndex("UploadedAt")
                        .HasDatabaseName("IX_SecurityIncidentAttachments_UploadedAt");

                    b.HasIndex("SecurityIncidentId", "AttachmentType")
                        .HasDatabaseName("IX_SecurityIncidentAttachments_Incident_Type");

                    b.ToTable("SecurityIncidentAttachments", (string)null);
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.Security.SecurityIncidentInvolvedPerson", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("AddedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("AdditionalNotes")
                        .HasColumnType("text");

                    b.Property<string>("ContactMethod")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<DateTime>("InvolvedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Involvement")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<bool>("IsReporter")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsSuspect")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsVictim")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsWitness")
                        .HasColumnType("boolean");

                    b.Property<int>("PersonId")
                        .HasColumnType("integer");

                    b.Property<int>("SecurityIncidentId")
                        .HasColumnType("integer");

                    b.Property<string>("Statement")
                        .HasColumnType("text");

                    b.Property<DateTime?>("StatementDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("StatementTaken")
                        .HasColumnType("boolean");

                    b.HasKey("Id");

                    b.HasIndex("IsSuspect")
                        .HasDatabaseName("IX_SecurityIncidentInvolvedPersons_IsSuspect");

                    b.HasIndex("IsVictim")
                        .HasDatabaseName("IX_SecurityIncidentInvolvedPersons_IsVictim");

                    b.HasIndex("IsWitness")
                        .HasDatabaseName("IX_SecurityIncidentInvolvedPersons_IsWitness");

                    b.HasIndex("PersonId")
                        .HasDatabaseName("IX_SecurityIncidentInvolvedPersons_PersonId");

                    b.HasIndex("SecurityIncidentId")
                        .HasDatabaseName("IX_SecurityIncidentInvolvedPersons_SecurityIncidentId");

                    b.HasIndex("StatementTaken")
                        .HasDatabaseName("IX_SecurityIncidentInvolvedPersons_StatementTaken");

                    b.HasIndex("SecurityIncidentId", "PersonId")
                        .IsUnique()
                        .HasDatabaseName("IX_SecurityIncidentInvolvedPersons_Incident_Person");

                    b.ToTable("SecurityIncidentInvolvedPersons", (string)null);
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.Security.SecurityIncidentResponse", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("ActionDateTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ActionTaken")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<decimal?>("Cost")
                        .HasColumnType("decimal(15,2)")
                        .HasAnnotation("CheckConstraint", "Cost >= 0");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<int?>("EffortHours")
                        .HasColumnType("integer")
                        .HasAnnotation("CheckConstraint", "EffortHours >= 0");

                    b.Property<string>("FollowUpDetails")
                        .HasColumnType("text");

                    b.Property<DateTime?>("FollowUpDueDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("FollowUpRequired")
                        .HasColumnType("boolean");

                    b.Property<string>("ResourcesUsed")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<int>("ResponderId")
                        .HasColumnType("integer");

                    b.Property<int>("ResponseType")
                        .HasColumnType("integer");

                    b.Property<int>("SecurityIncidentId")
                        .HasColumnType("integer");

                    b.Property<string>("ToolsUsed")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<bool>("WasSuccessful")
                        .HasColumnType("boolean");

                    b.HasKey("Id");

                    b.HasIndex("ActionDateTime")
                        .HasDatabaseName("IX_SecurityIncidentResponses_ActionDateTime");

                    b.HasIndex("FollowUpDueDate")
                        .HasDatabaseName("IX_SecurityIncidentResponses_FollowUpDueDate");

                    b.HasIndex("FollowUpRequired")
                        .HasDatabaseName("IX_SecurityIncidentResponses_FollowUpRequired");

                    b.HasIndex("ResponderId")
                        .HasDatabaseName("IX_SecurityIncidentResponses_ResponderId");

                    b.HasIndex("ResponseType")
                        .HasDatabaseName("IX_SecurityIncidentResponses_ResponseType");

                    b.HasIndex("SecurityIncidentId")
                        .HasDatabaseName("IX_SecurityIncidentResponses_SecurityIncidentId");

                    b.HasIndex("SecurityIncidentId", "ActionDateTime")
                        .HasDatabaseName("IX_SecurityIncidentResponses_Incident_DateTime");

                    b.HasIndex("SecurityIncidentId", "ResponseType")
                        .HasDatabaseName("IX_SecurityIncidentResponses_Incident_Type");

                    b.ToTable("SecurityIncidentResponses", (string)null);
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.Security.ThreatAssessment", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("AssessedById")
                        .HasColumnType("integer");

                    b.Property<DateTime>("AssessmentDateTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("AssessmentRationale")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("CurrentThreatLevel")
                        .HasColumnType("integer");

                    b.Property<bool>("ExternalThreatIntelUsed")
                        .HasColumnType("boolean");

                    b.Property<int>("ImpactPotential")
                        .HasColumnType("integer")
                        .HasAnnotation("CheckConstraint", "ImpactPotential BETWEEN 1 AND 5");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasColumnType("text");

                    b.Property<int>("PreviousThreatLevel")
                        .HasColumnType("integer");

                    b.Property<int>("SecurityIncidentId")
                        .HasColumnType("integer");

                    b.Property<int>("TargetVulnerability")
                        .HasColumnType("integer")
                        .HasAnnotation("CheckConstraint", "TargetVulnerability BETWEEN 1 AND 5");

                    b.Property<int>("ThreatCapability")
                        .HasColumnType("integer")
                        .HasAnnotation("CheckConstraint", "ThreatCapability BETWEEN 1 AND 5");

                    b.Property<string>("ThreatIntelDetails")
                        .HasColumnType("text");

                    b.Property<string>("ThreatIntelSource")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<int>("ThreatIntent")
                        .HasColumnType("integer")
                        .HasAnnotation("CheckConstraint", "ThreatIntent BETWEEN 1 AND 5");

                    b.HasKey("Id");

                    b.HasIndex("AssessedById");

                    b.HasIndex("AssessmentDateTime")
                        .HasDatabaseName("IX_ThreatAssessments_AssessmentDateTime");

                    b.HasIndex("CurrentThreatLevel")
                        .HasDatabaseName("IX_ThreatAssessments_CurrentThreatLevel");

                    b.HasIndex("SecurityIncidentId")
                        .HasDatabaseName("IX_ThreatAssessments_SecurityIncidentId");

                    b.HasIndex("SecurityIncidentId", "AssessmentDateTime")
                        .HasDatabaseName("IX_ThreatAssessments_Incident_DateTime");

                    b.ToTable("ThreatAssessments", (string)null);
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.Security.ThreatIndicator", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("Confidence")
                        .HasColumnType("integer")
                        .HasAnnotation("CheckConstraint", "Confidence BETWEEN 1 AND 100");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<DateTime>("FirstSeen")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("IndicatorType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("IndicatorValue")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<DateTime>("LastSeen")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Source")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("Tags")
                        .HasColumnType("text");

                    b.Property<string>("ThreatType")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.HasKey("Id");

                    b.HasIndex("Confidence")
                        .HasDatabaseName("IX_ThreatIndicators_Confidence");

                    b.HasIndex("FirstSeen")
                        .HasDatabaseName("IX_ThreatIndicators_FirstSeen");

                    b.HasIndex("IsActive")
                        .HasDatabaseName("IX_ThreatIndicators_IsActive");

                    b.HasIndex("LastSeen")
                        .HasDatabaseName("IX_ThreatIndicators_LastSeen");

                    b.HasIndex("Source")
                        .HasDatabaseName("IX_ThreatIndicators_Source");

                    b.HasIndex("ThreatType")
                        .HasDatabaseName("IX_ThreatIndicators_ThreatType");

                    b.HasIndex("IndicatorType", "IndicatorValue")
                        .IsUnique()
                        .HasDatabaseName("IX_ThreatIndicators_Type_Value");

                    b.HasIndex("IsActive", "Confidence")
                        .HasDatabaseName("IX_ThreatIndicators_Active_Confidence");

                    b.HasIndex("ThreatType", "IsActive")
                        .HasDatabaseName("IX_ThreatIndicators_ThreatType_Active");

                    b.ToTable("ThreatIndicators", (string)null);
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.Training", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime?>("ActualEndDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("ActualStartDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("AssessmentMethod")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<decimal>("AverageRating")
                        .HasPrecision(3, 2)
                        .HasColumnType("numeric(3,2)");

                    b.Property<string>("Category")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("CertificateValidityPeriod")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("CertificationType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("CertifyingBody")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<decimal>("CostPerParticipant")
                        .HasPrecision(18, 2)
                        .HasColumnType("numeric(18,2)");

                    b.Property<string>("CourseOutline")
                        .IsRequired()
                        .HasMaxLength(4000)
                        .HasColumnType("character varying(4000)");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Currency")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)")
                        .HasDefaultValue("IDR");

                    b.Property<string>("DeliveryMethod")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<int>("DurationHours")
                        .HasColumnType("integer");

                    b.Property<string>("EvaluationSummary")
                        .IsRequired()
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<string>("ImprovementActions")
                        .IsRequired()
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<string>("IndonesianTrainingStandard")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("InstructorContact")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("InstructorName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("InstructorQualifications")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<bool>("IsBPJSCompliant")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsExternalInstructor")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsK3MandatoryTraining")
                        .HasColumnType("boolean");

                    b.Property<bool>("IssuesCertificate")
                        .HasColumnType("boolean");

                    b.Property<string>("K3RegulationReference")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("LearningObjectives")
                        .IsRequired()
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<string>("Materials")
                        .IsRequired()
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<int>("MaxParticipants")
                        .HasColumnType("integer");

                    b.Property<int>("MinParticipants")
                        .HasColumnType("integer");

                    b.Property<string>("MinistryApprovalNumber")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("OnlineLink")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("OnlinePlatform")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<decimal>("PassingScore")
                        .HasPrecision(5, 2)
                        .HasColumnType("numeric(5,2)");

                    b.Property<string>("Prerequisites")
                        .IsRequired()
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<string>("Priority")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<bool>("RequiresGovernmentCertification")
                        .HasColumnType("boolean");

                    b.Property<DateTime>("ScheduledEndDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("ScheduledStartDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<decimal>("TotalBudget")
                        .HasPrecision(18, 2)
                        .HasColumnType("numeric(18,2)");

                    b.Property<int>("TotalRatings")
                        .HasColumnType("integer");

                    b.Property<string>("TrainingCode")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("Venue")
                        .IsRequired()
                        .HasMaxLength(300)
                        .HasColumnType("character varying(300)");

                    b.Property<string>("VenueAddress")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.HasKey("Id");

                    b.HasIndex("Category");

                    b.HasIndex("CreatedAt");

                    b.HasIndex("DeliveryMethod");

                    b.HasIndex("InstructorName")
                        .HasDatabaseName("IX_Trainings_InstructorName_Search");

                    b.HasIndex("Priority");

                    b.HasIndex("ScheduledEndDate");

                    b.HasIndex("ScheduledStartDate");

                    b.HasIndex("Status");

                    b.HasIndex("Title")
                        .HasDatabaseName("IX_Trainings_Title_Search");

                    b.HasIndex("TrainingCode")
                        .IsUnique();

                    b.HasIndex("Type");

                    b.HasIndex("Venue")
                        .HasDatabaseName("IX_Trainings_Venue_Search");

                    b.HasIndex("Category", "Priority");

                    b.HasIndex("DeliveryMethod", "Status");

                    b.HasIndex("IsK3MandatoryTraining", "Status");

                    b.HasIndex("Status", "MaxParticipants")
                        .HasDatabaseName("IX_Trainings_AvailableSpots");

                    b.HasIndex("Status", "ScheduledStartDate")
                        .HasDatabaseName("IX_Trainings_Overdue_Query");

                    b.HasIndex("Status", "Type");

                    b.HasIndex("Type", "Category");

                    b.HasIndex("Status", "Priority", "ScheduledStartDate");

                    b.HasIndex("Type", "Status", "CreatedAt");

                    b.ToTable("Trainings", (string)null);
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.TrainingAttachment", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime?>("ApprovalDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ApprovedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime?>("ArchivedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("AttachmentType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("ChecksumMD5")
                        .IsRequired()
                        .HasMaxLength(32)
                        .HasColumnType("character varying(32)");

                    b.Property<string>("ChecksumSHA256")
                        .IsRequired()
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.Property<string>("ContentType")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<int>("DownloadCount")
                        .HasColumnType("integer");

                    b.Property<string>("FileName")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("FilePath")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<long>("FileSize")
                        .HasColumnType("bigint");

                    b.Property<bool>("HasDigitalSignature")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<bool>("IsApproved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<bool>("IsArchived")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<bool>("IsComplianceDocument")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<bool>("IsCurrentVersion")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true);

                    b.Property<bool>("IsInstructorOnly")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<bool>("IsK3Document")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<bool>("IsParticipantSubmission")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<bool>("IsPublic")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<bool>("IsTranslationRequired")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<bool>("IsVirusClean")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true);

                    b.Property<bool>("IsVirusScanned")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<string>("K3DocumentType")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Language")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)")
                        .HasDefaultValue("id-ID");

                    b.Property<DateTime?>("LastAccessedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastAccessedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("OriginalFileName")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<int?>("PreviousVersionId")
                        .HasColumnType("integer");

                    b.Property<string>("RegulatoryReference")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<bool>("RequiresApproval")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<string>("SignatureInfo")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<int?>("SubmittedByParticipantId")
                        .HasColumnType("integer");

                    b.Property<int>("TrainingId")
                        .HasColumnType("integer");

                    b.Property<string>("TranslatedFrom")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)");

                    b.Property<DateTime>("UploadedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UploadedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<int>("Version")
                        .HasColumnType("integer");

                    b.Property<string>("VersionNotes")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<DateTime?>("VirusScanDate")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("AttachmentType");

                    b.HasIndex("IsApproved");

                    b.HasIndex("IsComplianceDocument");

                    b.HasIndex("IsCurrentVersion");

                    b.HasIndex("IsPublic");

                    b.HasIndex("SubmittedByParticipantId");

                    b.HasIndex("TrainingId");

                    b.HasIndex("UploadedAt");

                    b.HasIndex("IsPublic", "IsApproved");

                    b.HasIndex("TrainingId", "AttachmentType");

                    b.HasIndex("TrainingId", "IsCurrentVersion");

                    b.HasIndex("TrainingId", "IsK3Document");

                    b.ToTable("TrainingAttachments", (string)null);
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.TrainingCertification", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("BPJSReference")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("CPDCategory")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<decimal?>("CPDCreditsEarned")
                        .HasPrecision(5, 2)
                        .HasColumnType("numeric(5,2)");

                    b.Property<string>("CertificateFileHash")
                        .IsRequired()
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.Property<string>("CertificateFilePath")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("CertificateNumber")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("CertificateTitle")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("CertificationType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("CertifyingBody")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("ComplianceStatus")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasDefaultValue("Compliant");

                    b.Property<bool>("CountsTowardsCPD")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("DigitalSignature")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<decimal?>("FinalScore")
                        .HasPrecision(5, 2)
                        .HasColumnType("numeric(5,2)");

                    b.Property<string>("GeographicScope")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasDefaultValue("Indonesia");

                    b.Property<string>("Grade")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<bool>("HasWatermark")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<string>("IndonesianStandardReference")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("IndustryScope")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<bool>("IsActiveCredential")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true);

                    b.Property<bool>("IsBPJSCompliant")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<bool>("IsGovernmentRecognized")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<bool>("IsK3Certificate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<bool>("IsRenewal")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<bool>("IsRevoked")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<bool>("IsValid")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true);

                    b.Property<bool>("IsVerified")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true);

                    b.Property<string>("IssuedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("IssuedByOrganization")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("IssuedByTitle")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime>("IssuedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("IssuerLicenseNumber")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("K3CertificateType")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("K3LicenseClass")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime?>("LastAuditDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastAuditResult")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime?>("LastVerificationAttempt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("MinistryApprovalNumber")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<decimal?>("PassingScore")
                        .HasPrecision(5, 2)
                        .HasColumnType("numeric(5,2)");

                    b.Property<string>("PerformanceNotes")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<string>("ProfessionalBodyReference")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("QRCodeData")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<DateTime?>("RenewalDueDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("RenewalReminderSent")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<string>("RenewalRequirements")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<int?>("RenewedFromCertificateId")
                        .HasColumnType("integer");

                    b.Property<bool>("RequiresPeriodicAudit")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<bool>("RequiresRenewal")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<string>("RevocationReason")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("RevokedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime?>("RevokedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("TrainingId")
                        .HasColumnType("integer");

                    b.Property<string>("UsageRestrictions")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<int>("UserId")
                        .HasColumnType("integer");

                    b.Property<string>("UserName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime?>("ValidUntil")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("VerificationAttempts")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("VerificationDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("VerificationMethod")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("VerificationUrl")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("VerifiedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.HasKey("Id");

                    b.HasIndex("CertificateNumber")
                        .IsUnique();

                    b.HasIndex("IsK3Certificate");

                    b.HasIndex("IsRevoked");

                    b.HasIndex("IsValid");

                    b.HasIndex("IssuedDate");

                    b.HasIndex("RenewalDueDate");

                    b.HasIndex("RenewedFromCertificateId");

                    b.HasIndex("RequiresRenewal");

                    b.HasIndex("TrainingId");

                    b.HasIndex("UserId");

                    b.HasIndex("ValidUntil");

                    b.HasIndex("IsK3Certificate", "K3CertificateType");

                    b.HasIndex("IsValid", "ValidUntil");

                    b.HasIndex("RequiresRenewal", "RenewalDueDate");

                    b.HasIndex("TrainingId", "UserId");

                    b.HasIndex("UserId", "IsValid");

                    b.ToTable("TrainingCertifications", (string)null);
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.TrainingComment", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("AttachmentPath")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<int?>("AuthorId")
                        .HasColumnType("integer");

                    b.Property<string>("AuthorName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("AuthorRole")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime>("CommentDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CommentType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("Content")
                        .IsRequired()
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<int>("EditCount")
                        .HasColumnType("integer");

                    b.Property<string>("EditReason")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("FeedbackCategory")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<bool>("IsAnonymous")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<bool>("IsApproved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true);

                    b.Property<bool>("IsComplianceNote")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<bool>("IsEdited")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<bool>("IsImportant")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<bool>("IsInstructorOnly")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<bool>("IsK3Related")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<bool>("IsModerated")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<bool>("IsPinned")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsPrivateNote")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<bool>("IsPublic")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true);

                    b.Property<bool>("IsReply")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsResolved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<bool>("IsSystemGenerated")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<string>("K3IssueType")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<DateTime?>("LastEditedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<int>("LikeCount")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0);

                    b.Property<string>("ModeratedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime?>("ModerationDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ModerationNotes")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<int?>("ParentCommentId")
                        .HasColumnType("integer");

                    b.Property<string>("PinnedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime?>("PinnedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ReferencedDocuments")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<string>("RegulatoryContext")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<decimal?>("RelatedRating")
                        .HasPrecision(3, 2)
                        .HasColumnType("numeric(3,2)");

                    b.Property<int>("ReplyCount")
                        .HasColumnType("integer");

                    b.Property<bool>("RequiresResponse")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<string>("ResolvedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime?>("ResolvedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Tags")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<int>("TrainingId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("AuthorId");

                    b.HasIndex("CommentDate");

                    b.HasIndex("CommentType");

                    b.HasIndex("IsImportant");

                    b.HasIndex("IsPinned");

                    b.HasIndex("IsPublic");

                    b.HasIndex("ParentCommentId");

                    b.HasIndex("TrainingId");

                    b.HasIndex("AuthorId", "CommentDate");

                    b.HasIndex("TrainingId", "CommentType");

                    b.HasIndex("TrainingId", "IsPinned");

                    b.HasIndex("TrainingId", "IsPublic");

                    b.ToTable("TrainingComments", (string)null);
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.TrainingParticipant", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime?>("AssessmentDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("AssessmentMethodUsed")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("AssessmentNotes")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<DateTime?>("AttendanceEndTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("AttendanceNotes")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<decimal>("AttendancePercentage")
                        .HasPrecision(5, 2)
                        .HasColumnType("numeric(5,2)");

                    b.Property<DateTime?>("AttendanceStartTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("BPJSNumber")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime?>("CertificateIssuedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CertificateNumber")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime?>("CompletedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CompletionNotes")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("EmployeeId")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime>("EnrolledAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("EnrolledBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<decimal?>("FinalScore")
                        .HasPrecision(5, 2)
                        .HasColumnType("numeric(5,2)");

                    b.Property<bool>("HasMetPrerequisites")
                        .HasColumnType("boolean");

                    b.Property<bool>("HasPassed")
                        .HasColumnType("boolean");

                    b.Property<string>("InstructorFeedback")
                        .IsRequired()
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<bool>("IsBPJSRegistered")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsEligibleForCertificate")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsIndonesianCitizen")
                        .HasColumnType("boolean");

                    b.Property<string>("K3LicenseNumber")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("PrerequisiteNotes")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<DateTime?>("PrerequisiteVerificationDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("TrainingFeedback")
                        .IsRequired()
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<int>("TrainingId")
                        .HasColumnType("integer");

                    b.Property<decimal?>("TrainingRating")
                        .HasPrecision(3, 2)
                        .HasColumnType("numeric(3,2)");

                    b.Property<string>("UserDepartment")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("UserEmail")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<int>("UserId")
                        .HasColumnType("integer");

                    b.Property<string>("UserName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("UserPhone")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("UserPosition")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("WorkPermitNumber")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.HasKey("Id");

                    b.HasIndex("EnrolledAt");

                    b.HasIndex("IsEligibleForCertificate");

                    b.HasIndex("Status");

                    b.HasIndex("TrainingId");

                    b.HasIndex("UserId");

                    b.HasIndex("Status", "IsEligibleForCertificate");

                    b.HasIndex("TrainingId", "Status");

                    b.HasIndex("TrainingId", "UserId")
                        .IsUnique();

                    b.HasIndex("UserId", "Status");

                    b.ToTable("TrainingParticipants", (string)null);
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.TrainingRequirement", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("AssignedBy")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<DateTime>("AssignedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("AssignedTo")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("AttachmentPath")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("CompletedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime?>("CompletedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CompletionNotes")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<decimal?>("ComplianceCost")
                        .HasPrecision(10, 2)
                        .HasColumnType("numeric(10,2)");

                    b.Property<string>("ComplianceNotes")
                        .IsRequired()
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("DocumentationRequired")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<DateTime?>("DueDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("EvidenceProvided")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<bool>("IsBPJSRelated")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsGovernmentMandated")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsK3Requirement")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsMandatory")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsOverdue")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsVerified")
                        .HasColumnType("boolean");

                    b.Property<string>("K3RegulationReference")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<int>("Priority")
                        .HasColumnType("integer");

                    b.Property<string>("RegulatoryReference")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("RequirementDescription")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<bool>("RequiresVerification")
                        .HasColumnType("boolean");

                    b.Property<string>("RiskLevelIfNotCompleted")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<int>("TrainingId")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("VerificationDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("VerificationMethod")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("VerifiedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.HasKey("Id");

                    b.HasIndex("DueDate");

                    b.HasIndex("IsMandatory");

                    b.HasIndex("IsVerified");

                    b.HasIndex("Status");

                    b.HasIndex("TrainingId");

                    b.HasIndex("Status", "DueDate");

                    b.HasIndex("TrainingId", "IsMandatory");

                    b.HasIndex("TrainingId", "Status");

                    b.ToTable("TrainingRequirements", (string)null);
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.User", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Department")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("EmployeeId")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasColumnType("text");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("PasswordHash")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Position")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.HasKey("Id");

                    b.HasIndex("Email")
                        .IsUnique();

                    b.HasIndex("EmployeeId")
                        .IsUnique();

                    b.ToTable("Users");
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.UserRole", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasColumnType("text");

                    b.Property<int>("RoleId")
                        .HasColumnType("integer");

                    b.Property<int>("UserId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("RoleId");

                    b.HasIndex("UserId", "RoleId")
                        .IsUnique();

                    b.ToTable("UserRoles");
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.VaccinationRecord", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("AdministeredBy")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("AdministrationLocation")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("BatchNumber")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime?>("DateAdministered")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ExemptionReason")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<DateTime?>("ExpiryDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("HealthRecordId")
                        .HasColumnType("integer");

                    b.Property<bool>("IsRequired")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Notes")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<int>("Status")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(1);

                    b.Property<string>("VaccineName")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt");

                    b.HasIndex("DateAdministered");

                    b.HasIndex("ExpiryDate");

                    b.HasIndex("HealthRecordId");

                    b.HasIndex("IsRequired");

                    b.HasIndex("Status");

                    b.HasIndex("VaccineName");

                    b.HasIndex("HealthRecordId", "VaccineName", "Status");

                    b.ToTable("VaccinationRecords", (string)null);
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.WorkPermit", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime?>("ActualEndDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("ActualStartDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CompanyWorkPermitNumber")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("CompletionNotes")
                        .IsRequired()
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<string>("ContactPhone")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<string>("ContractorCompany")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<string>("EmergencyProcedures")
                        .IsRequired()
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<string>("EnvironmentalPermitNumber")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("EquipmentToBeUsed")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<int>("EstimatedDuration")
                        .HasColumnType("integer");

                    b.Property<bool>("HasSMK3Compliance")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsCompletedSafely")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsJamsostekCompliant")
                        .HasColumnType("boolean");

                    b.Property<string>("K3LicenseNumber")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("LessonsLearned")
                        .IsRequired()
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<string>("MaterialsInvolved")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<int>("NumberOfWorkers")
                        .HasColumnType("integer");

                    b.Property<string>("PermitNumber")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime>("PlannedEndDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("PlannedStartDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Priority")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("RequestedByDepartment")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<int>("RequestedById")
                        .HasColumnType("integer");

                    b.Property<string>("RequestedByName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("RequestedByPosition")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<bool>("RequiresConfinedSpaceEntry")
                        .HasColumnType("boolean");

                    b.Property<bool>("RequiresElectricalIsolation")
                        .HasColumnType("boolean");

                    b.Property<bool>("RequiresExcavation")
                        .HasColumnType("boolean");

                    b.Property<bool>("RequiresFireWatch")
                        .HasColumnType("boolean");

                    b.Property<bool>("RequiresGasMonitoring")
                        .HasColumnType("boolean");

                    b.Property<bool>("RequiresHeightWork")
                        .HasColumnType("boolean");

                    b.Property<bool>("RequiresHotWorkPermit")
                        .HasColumnType("boolean");

                    b.Property<bool>("RequiresRadiationWork")
                        .HasColumnType("boolean");

                    b.Property<string>("RiskAssessmentSummary")
                        .IsRequired()
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<string>("RiskLevel")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("SafetyOfficer")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("WorkLocation")
                        .IsRequired()
                        .HasMaxLength(300)
                        .HasColumnType("character varying(300)");

                    b.Property<string>("WorkScope")
                        .IsRequired()
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<string>("WorkSupervisor")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt");

                    b.HasIndex("PermitNumber")
                        .IsUnique();

                    b.HasIndex("PlannedEndDate");

                    b.HasIndex("PlannedStartDate");

                    b.HasIndex("Priority");

                    b.HasIndex("RequestedById");

                    b.HasIndex("RiskLevel");

                    b.HasIndex("Status");

                    b.HasIndex("Type");

                    b.HasIndex("PlannedStartDate", "Status");

                    b.HasIndex("RequestedById", "Status");

                    b.HasIndex("Status", "Type");

                    b.ToTable("WorkPermits", (string)null);
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.WorkPermitApproval", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("ApprovalLevel")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<int>("ApprovalOrder")
                        .HasColumnType("integer");

                    b.Property<DateTime>("ApprovedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("ApprovedById")
                        .HasColumnType("integer");

                    b.Property<string>("ApprovedByName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("AuthorityLevel")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Comments")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<bool>("HasAuthorityToApprove")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsApproved")
                        .HasColumnType("boolean");

                    b.Property<string>("K3CertificateNumber")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Signature")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<int>("WorkPermitId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("ApprovalLevel");

                    b.HasIndex("ApprovedAt");

                    b.HasIndex("ApprovedById");

                    b.HasIndex("IsApproved");

                    b.HasIndex("WorkPermitId");

                    b.HasIndex("WorkPermitId", "ApprovalLevel");

                    b.HasIndex("WorkPermitId", "ApprovalOrder");

                    b.ToTable("WorkPermitApprovals", (string)null);
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.WorkPermitAttachment", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("AttachmentType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("ContentType")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("FileName")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("FilePath")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<long>("FileSize")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("OriginalFileName")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<DateTime>("UploadedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UploadedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<int>("WorkPermitId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("AttachmentType");

                    b.HasIndex("UploadedAt");

                    b.HasIndex("WorkPermitId");

                    b.ToTable("WorkPermitAttachments", (string)null);
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.WorkPermitHazard", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int?>("CategoryId")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("ControlImplementedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ControlMeasures")
                        .IsRequired()
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("HazardDescription")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<string>("ImplementationNotes")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<bool>("IsControlImplemented")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<int>("Likelihood")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(1);

                    b.Property<string>("ResidualRiskLevel")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("ResponsiblePerson")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("RiskLevel")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<int>("Severity")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(1);

                    b.Property<int>("WorkPermitId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("CategoryId");

                    b.HasIndex("IsControlImplemented");

                    b.HasIndex("RiskLevel");

                    b.HasIndex("WorkPermitId");

                    b.HasIndex("WorkPermitId", "IsControlImplemented");

                    b.HasIndex("WorkPermitId", "RiskLevel");

                    b.ToTable("WorkPermitHazards", (string)null);
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.WorkPermitPrecaution", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Category")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime?>("CompletedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CompletedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("CompletionNotes")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<bool>("IsCompleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsK3Requirement")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsMandatoryByLaw")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsRequired")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsVerified")
                        .HasColumnType("boolean");

                    b.Property<string>("K3StandardReference")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("PrecautionDescription")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<int>("Priority")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(1);

                    b.Property<bool>("RequiresVerification")
                        .HasColumnType("boolean");

                    b.Property<string>("ResponsiblePerson")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("VerificationMethod")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<DateTime?>("VerifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("VerifiedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<int>("WorkPermitId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("Category");

                    b.HasIndex("IsCompleted");

                    b.HasIndex("IsK3Requirement");

                    b.HasIndex("IsMandatoryByLaw");

                    b.HasIndex("IsRequired");

                    b.HasIndex("IsVerified");

                    b.HasIndex("Priority");

                    b.HasIndex("WorkPermitId");

                    b.HasIndex("IsK3Requirement", "IsCompleted");

                    b.HasIndex("WorkPermitId", "IsCompleted");

                    b.HasIndex("WorkPermitId", "IsRequired");

                    b.HasIndex("WorkPermitId", "Priority");

                    b.ToTable("WorkPermitPrecautions", (string)null);
                });

            modelBuilder.Entity("RolePermissions", b =>
                {
                    b.Property<int>("RoleId")
                        .HasColumnType("integer");

                    b.Property<int>("PermissionId")
                        .HasColumnType("integer");

                    b.HasKey("RoleId", "PermissionId");

                    b.HasIndex("PermissionId");

                    b.ToTable("RolePermissions");
                });

            modelBuilder.Entity("SecurityIncidentControls", b =>
                {
                    b.Property<int>("SecurityControlId")
                        .HasColumnType("integer");

                    b.Property<int>("SecurityIncidentId")
                        .HasColumnType("integer");

                    b.HasKey("SecurityControlId", "SecurityIncidentId");

                    b.HasIndex("SecurityIncidentId");

                    b.ToTable("SecurityIncidentControls");
                });

            modelBuilder.Entity("SecurityIncidentIndicators", b =>
                {
                    b.Property<int>("SecurityIncidentId")
                        .HasColumnType("integer");

                    b.Property<int>("ThreatIndicatorId")
                        .HasColumnType("integer");

                    b.Property<string>("Context")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<DateTime>("DetectedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.HasKey("SecurityIncidentId", "ThreatIndicatorId");

                    b.HasIndex("ThreatIndicatorId");

                    b.ToTable("SecurityIncidentIndicators");
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.Audits.Audit", b =>
                {
                    b.HasOne("Harmoni360.Domain.Entities.User", "Auditor")
                        .WithMany()
                        .HasForeignKey("AuditorId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("Harmoni360.Domain.Entities.Department", "Department")
                        .WithMany()
                        .HasForeignKey("DepartmentId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("Auditor");

                    b.Navigation("Department");
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.Audits.AuditAttachment", b =>
                {
                    b.HasOne("Harmoni360.Domain.Entities.Audits.Audit", "Audit")
                        .WithMany("Attachments")
                        .HasForeignKey("AuditId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Harmoni360.Domain.Entities.Audits.AuditItem", "AuditItem")
                        .WithMany()
                        .HasForeignKey("AuditItemId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("Audit");

                    b.Navigation("AuditItem");
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.Audits.AuditComment", b =>
                {
                    b.HasOne("Harmoni360.Domain.Entities.Audits.AuditFinding", "AuditFinding")
                        .WithMany()
                        .HasForeignKey("AuditFindingId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("Harmoni360.Domain.Entities.Audits.Audit", "Audit")
                        .WithMany("Comments")
                        .HasForeignKey("AuditId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Harmoni360.Domain.Entities.Audits.AuditItem", "AuditItem")
                        .WithMany()
                        .HasForeignKey("AuditItemId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("Audit");

                    b.Navigation("AuditFinding");

                    b.Navigation("AuditItem");
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.Audits.AuditFinding", b =>
                {
                    b.HasOne("Harmoni360.Domain.Entities.Audits.Audit", "Audit")
                        .WithMany("Findings")
                        .HasForeignKey("AuditId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Harmoni360.Domain.Entities.Audits.AuditItem", "AuditItem")
                        .WithMany()
                        .HasForeignKey("AuditItemId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("Harmoni360.Domain.Entities.User", "ResponsiblePerson")
                        .WithMany()
                        .HasForeignKey("ResponsiblePersonId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("Audit");

                    b.Navigation("AuditItem");

                    b.Navigation("ResponsiblePerson");
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.Audits.AuditItem", b =>
                {
                    b.HasOne("Harmoni360.Domain.Entities.Audits.Audit", "Audit")
                        .WithMany("Items")
                        .HasForeignKey("AuditId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Harmoni360.Domain.Entities.User", "ResponsiblePerson")
                        .WithMany()
                        .HasForeignKey("ResponsiblePersonId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("Audit");

                    b.Navigation("ResponsiblePerson");
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.Audits.FindingAttachment", b =>
                {
                    b.HasOne("Harmoni360.Domain.Entities.Audits.AuditFinding", "AuditFinding")
                        .WithMany("Attachments")
                        .HasForeignKey("AuditFindingId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("AuditFinding");
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.CorrectiveAction", b =>
                {
                    b.HasOne("Harmoni360.Domain.Entities.User", "AssignedTo")
                        .WithMany()
                        .HasForeignKey("AssignedToId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("Harmoni360.Domain.Entities.Department", "Department")
                        .WithMany("CorrectiveActions")
                        .HasForeignKey("DepartmentId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("Harmoni360.Domain.Entities.Incident", null)
                        .WithMany("CorrectiveActions")
                        .HasForeignKey("IncidentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("AssignedTo");

                    b.Navigation("Department");
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.EmergencyContact", b =>
                {
                    b.HasOne("Harmoni360.Domain.Entities.HealthRecord", "HealthRecord")
                        .WithMany("EmergencyContacts")
                        .HasForeignKey("HealthRecordId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("HealthRecord");
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.EscalationAction", b =>
                {
                    b.HasOne("Harmoni360.Domain.Entities.EscalationRule", "EscalationRule")
                        .WithMany("Actions")
                        .HasForeignKey("EscalationRuleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("EscalationRule");
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.EscalationHistory", b =>
                {
                    b.HasOne("Harmoni360.Domain.Entities.EscalationRule", "EscalationRule")
                        .WithMany()
                        .HasForeignKey("EscalationRuleId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("Harmoni360.Domain.Entities.Incident", "Incident")
                        .WithMany()
                        .HasForeignKey("IncidentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("EscalationRule");

                    b.Navigation("Incident");
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.Hazard", b =>
                {
                    b.HasOne("Harmoni360.Domain.Entities.HazardCategory", "Category")
                        .WithMany("Hazards")
                        .HasForeignKey("CategoryId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("Harmoni360.Domain.Entities.RiskAssessment", "CurrentRiskAssessment")
                        .WithOne()
                        .HasForeignKey("Harmoni360.Domain.Entities.Hazard", "CurrentRiskAssessmentId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("Harmoni360.Domain.Entities.User", "Reporter")
                        .WithMany()
                        .HasForeignKey("ReporterId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("Harmoni360.Domain.Entities.HazardType", "Type")
                        .WithMany("Hazards")
                        .HasForeignKey("TypeId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.OwnsOne("Harmoni360.Domain.ValueObjects.GeoLocation", "GeoLocation", b1 =>
                        {
                            b1.Property<int>("HazardId")
                                .HasColumnType("integer");

                            b1.Property<double>("Latitude")
                                .HasPrecision(18, 6)
                                .HasColumnType("double precision")
                                .HasColumnName("Latitude");

                            b1.Property<double>("Longitude")
                                .HasPrecision(18, 6)
                                .HasColumnType("double precision")
                                .HasColumnName("Longitude");

                            b1.HasKey("HazardId");

                            b1.ToTable("Hazards");

                            b1.WithOwner()
                                .HasForeignKey("HazardId");
                        });

                    b.Navigation("Category");

                    b.Navigation("CurrentRiskAssessment");

                    b.Navigation("GeoLocation");

                    b.Navigation("Reporter");

                    b.Navigation("Type");
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.HazardAttachment", b =>
                {
                    b.HasOne("Harmoni360.Domain.Entities.Hazard", "Hazard")
                        .WithMany("Attachments")
                        .HasForeignKey("HazardId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Hazard");
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.HazardAuditLog", b =>
                {
                    b.HasOne("Harmoni360.Domain.Entities.Hazard", "Hazard")
                        .WithMany()
                        .HasForeignKey("HazardId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Hazard");
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.HazardMitigationAction", b =>
                {
                    b.HasOne("Harmoni360.Domain.Entities.User", "AssignedTo")
                        .WithMany()
                        .HasForeignKey("AssignedToId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("Harmoni360.Domain.Entities.Hazard", "Hazard")
                        .WithMany("MitigationActions")
                        .HasForeignKey("HazardId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Harmoni360.Domain.Entities.User", "VerifiedBy")
                        .WithMany()
                        .HasForeignKey("VerifiedById")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("AssignedTo");

                    b.Navigation("Hazard");

                    b.Navigation("VerifiedBy");
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.HazardReassessment", b =>
                {
                    b.HasOne("Harmoni360.Domain.Entities.User", "CompletedBy")
                        .WithMany()
                        .HasForeignKey("CompletedById")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Harmoni360.Domain.Entities.Hazard", "Hazard")
                        .WithMany("Reassessments")
                        .HasForeignKey("HazardId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("CompletedBy");

                    b.Navigation("Hazard");
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.HazardType", b =>
                {
                    b.HasOne("Harmoni360.Domain.Entities.HazardCategory", "Category")
                        .WithMany("HazardTypes")
                        .HasForeignKey("CategoryId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("Category");
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.HealthIncident", b =>
                {
                    b.HasOne("Harmoni360.Domain.Entities.HealthRecord", "HealthRecord")
                        .WithMany("HealthIncidents")
                        .HasForeignKey("HealthRecordId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Harmoni360.Domain.Entities.Incident", "Incident")
                        .WithMany()
                        .HasForeignKey("IncidentId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("HealthRecord");

                    b.Navigation("Incident");
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.HealthRecord", b =>
                {
                    b.HasOne("Harmoni360.Domain.Entities.User", "Person")
                        .WithMany()
                        .HasForeignKey("PersonId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Person");
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.Incident", b =>
                {
                    b.HasOne("Harmoni360.Domain.Entities.IncidentCategory", "Category")
                        .WithMany("Incidents")
                        .HasForeignKey("CategoryId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("Harmoni360.Domain.Entities.Department", "DepartmentEntity")
                        .WithMany("Incidents")
                        .HasForeignKey("DepartmentId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("Harmoni360.Domain.Entities.User", "Investigator")
                        .WithMany()
                        .HasForeignKey("InvestigatorId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("Harmoni360.Domain.Entities.IncidentLocation", "LocationEntity")
                        .WithMany("Incidents")
                        .HasForeignKey("LocationId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("Harmoni360.Domain.Entities.User", "Reporter")
                        .WithMany()
                        .HasForeignKey("ReporterId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.OwnsOne("Harmoni360.Domain.ValueObjects.GeoLocation", "GeoLocation", b1 =>
                        {
                            b1.Property<int>("IncidentId")
                                .HasColumnType("integer");

                            b1.Property<double>("Latitude")
                                .HasPrecision(18, 6)
                                .HasColumnType("double precision")
                                .HasColumnName("Latitude");

                            b1.Property<double>("Longitude")
                                .HasPrecision(18, 6)
                                .HasColumnType("double precision")
                                .HasColumnName("Longitude");

                            b1.HasKey("IncidentId");

                            b1.ToTable("Incidents");

                            b1.WithOwner()
                                .HasForeignKey("IncidentId");
                        });

                    b.Navigation("Category");

                    b.Navigation("DepartmentEntity");

                    b.Navigation("GeoLocation");

                    b.Navigation("Investigator");

                    b.Navigation("LocationEntity");

                    b.Navigation("Reporter");
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.IncidentAttachment", b =>
                {
                    b.HasOne("Harmoni360.Domain.Entities.Incident", null)
                        .WithMany("Attachments")
                        .HasForeignKey("IncidentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.IncidentAuditLog", b =>
                {
                    b.HasOne("Harmoni360.Domain.Entities.Incident", "Incident")
                        .WithMany()
                        .HasForeignKey("IncidentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Incident");
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.IncidentInvolvedPerson", b =>
                {
                    b.HasOne("Harmoni360.Domain.Entities.Incident", null)
                        .WithMany("InvolvedPersons")
                        .HasForeignKey("IncidentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Harmoni360.Domain.Entities.User", "Person")
                        .WithMany()
                        .HasForeignKey("PersonId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("Person");
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.IncidentLocation", b =>
                {
                    b.OwnsOne("Harmoni360.Domain.ValueObjects.GeoLocation", "GeoLocation", b1 =>
                        {
                            b1.Property<int>("IncidentLocationId")
                                .HasColumnType("integer");

                            b1.Property<double>("Latitude")
                                .HasPrecision(10, 8)
                                .HasColumnType("double precision")
                                .HasColumnName("Latitude");

                            b1.Property<double>("Longitude")
                                .HasPrecision(11, 8)
                                .HasColumnType("double precision")
                                .HasColumnName("Longitude");

                            b1.HasKey("IncidentLocationId");

                            b1.ToTable("IncidentLocations");

                            b1.WithOwner()
                                .HasForeignKey("IncidentLocationId");
                        });

                    b.Navigation("GeoLocation");
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.Inspections.FindingAttachment", b =>
                {
                    b.HasOne("Harmoni360.Domain.Entities.Inspections.InspectionFinding", "Finding")
                        .WithMany("Attachments")
                        .HasForeignKey("FindingId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Finding");
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.Inspections.Inspection", b =>
                {
                    b.HasOne("Harmoni360.Domain.Entities.Department", "Department")
                        .WithMany()
                        .HasForeignKey("DepartmentId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("Harmoni360.Domain.Entities.User", "Inspector")
                        .WithMany()
                        .HasForeignKey("InspectorId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Department");

                    b.Navigation("Inspector");
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.Inspections.InspectionAttachment", b =>
                {
                    b.HasOne("Harmoni360.Domain.Entities.Inspections.Inspection", "Inspection")
                        .WithMany("Attachments")
                        .HasForeignKey("InspectionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Inspection");
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.Inspections.InspectionComment", b =>
                {
                    b.HasOne("Harmoni360.Domain.Entities.Inspections.Inspection", "Inspection")
                        .WithMany("Comments")
                        .HasForeignKey("InspectionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Harmoni360.Domain.Entities.Inspections.InspectionComment", "ParentComment")
                        .WithMany("Replies")
                        .HasForeignKey("ParentCommentId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Harmoni360.Domain.Entities.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Inspection");

                    b.Navigation("ParentComment");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.Inspections.InspectionFinding", b =>
                {
                    b.HasOne("Harmoni360.Domain.Entities.Inspections.Inspection", "Inspection")
                        .WithMany("Findings")
                        .HasForeignKey("InspectionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Harmoni360.Domain.Entities.User", "ResponsiblePerson")
                        .WithMany()
                        .HasForeignKey("ResponsiblePersonId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("Inspection");

                    b.Navigation("ResponsiblePerson");
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.Inspections.InspectionItem", b =>
                {
                    b.HasOne("Harmoni360.Domain.Entities.Inspections.Inspection", "Inspection")
                        .WithMany("Items")
                        .HasForeignKey("InspectionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Inspection");
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.MedicalCondition", b =>
                {
                    b.HasOne("Harmoni360.Domain.Entities.HealthRecord", "HealthRecord")
                        .WithMany("MedicalConditions")
                        .HasForeignKey("HealthRecordId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("HealthRecord");
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.NotificationHistory", b =>
                {
                    b.HasOne("Harmoni360.Domain.Entities.Incident", "Incident")
                        .WithMany()
                        .HasForeignKey("IncidentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Incident");
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.PPEAssignment", b =>
                {
                    b.HasOne("Harmoni360.Domain.Entities.User", "AssignedTo")
                        .WithMany()
                        .HasForeignKey("AssignedToId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("Harmoni360.Domain.Entities.PPEItem", "PPEItem")
                        .WithMany("AssignmentHistory")
                        .HasForeignKey("PPEItemId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("AssignedTo");

                    b.Navigation("PPEItem");
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.PPEComplianceRequirement", b =>
                {
                    b.HasOne("Harmoni360.Domain.Entities.PPECategory", "Category")
                        .WithMany()
                        .HasForeignKey("CategoryId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Harmoni360.Domain.Entities.Role", "Role")
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Category");

                    b.Navigation("Role");
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.PPEInspection", b =>
                {
                    b.HasOne("Harmoni360.Domain.Entities.User", "Inspector")
                        .WithMany()
                        .HasForeignKey("InspectorId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("Harmoni360.Domain.Entities.PPEItem", "PPEItem")
                        .WithMany("Inspections")
                        .HasForeignKey("PPEItemId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Inspector");

                    b.Navigation("PPEItem");
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.PPEItem", b =>
                {
                    b.HasOne("Harmoni360.Domain.Entities.User", "AssignedTo")
                        .WithMany()
                        .HasForeignKey("AssignedToId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("Harmoni360.Domain.Entities.PPECategory", "Category")
                        .WithMany("PPEItems")
                        .HasForeignKey("CategoryId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("Harmoni360.Domain.Entities.PPESize", "Size")
                        .WithMany("PPEItems")
                        .HasForeignKey("SizeId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("Harmoni360.Domain.Entities.PPEStorageLocation", "StorageLocation")
                        .WithMany("PPEItems")
                        .HasForeignKey("StorageLocationId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.OwnsOne("Harmoni360.Domain.ValueObjects.CertificationInfo", "Certification", b1 =>
                        {
                            b1.Property<int>("PPEItemId")
                                .HasColumnType("integer");

                            b1.Property<DateTime>("CertificationDate")
                                .HasColumnType("timestamp with time zone")
                                .HasColumnName("CertificationDate");

                            b1.Property<string>("CertificationNumber")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("CertificationNumber");

                            b1.Property<string>("CertifyingBody")
                                .IsRequired()
                                .HasMaxLength(200)
                                .HasColumnType("character varying(200)")
                                .HasColumnName("CertifyingBody");

                            b1.Property<DateTime>("ExpiryDate")
                                .HasColumnType("timestamp with time zone")
                                .HasColumnName("CertificationExpiryDate");

                            b1.Property<string>("Standard")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("CertificationStandard");

                            b1.HasKey("PPEItemId");

                            b1.ToTable("PPEItems");

                            b1.WithOwner()
                                .HasForeignKey("PPEItemId");
                        });

                    b.OwnsOne("Harmoni360.Domain.ValueObjects.MaintenanceSchedule", "MaintenanceInfo", b1 =>
                        {
                            b1.Property<int>("PPEItemId")
                                .HasColumnType("integer");

                            b1.Property<int>("IntervalDays")
                                .HasColumnType("integer")
                                .HasColumnName("MaintenanceIntervalDays");

                            b1.Property<DateTime?>("LastMaintenanceDate")
                                .HasColumnType("timestamp with time zone")
                                .HasColumnName("LastMaintenanceDate");

                            b1.Property<string>("MaintenanceInstructions")
                                .HasMaxLength(1000)
                                .HasColumnType("character varying(1000)")
                                .HasColumnName("MaintenanceInstructions");

                            b1.Property<DateTime?>("NextMaintenanceDate")
                                .HasColumnType("timestamp with time zone")
                                .HasColumnName("NextMaintenanceDate");

                            b1.HasKey("PPEItemId");

                            b1.ToTable("PPEItems");

                            b1.WithOwner()
                                .HasForeignKey("PPEItemId");
                        });

                    b.Navigation("AssignedTo");

                    b.Navigation("Category");

                    b.Navigation("Certification");

                    b.Navigation("MaintenanceInfo");

                    b.Navigation("Size");

                    b.Navigation("StorageLocation");
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.PPERequest", b =>
                {
                    b.HasOne("Harmoni360.Domain.Entities.PPECategory", "Category")
                        .WithMany()
                        .HasForeignKey("CategoryId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("Harmoni360.Domain.Entities.PPEItem", "FulfilledPPEItem")
                        .WithMany()
                        .HasForeignKey("FulfilledPPEItemId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("Harmoni360.Domain.Entities.User", "Requester")
                        .WithMany()
                        .HasForeignKey("RequesterId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("Harmoni360.Domain.Entities.User", "Reviewer")
                        .WithMany()
                        .HasForeignKey("ReviewerId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("Category");

                    b.Navigation("FulfilledPPEItem");

                    b.Navigation("Requester");

                    b.Navigation("Reviewer");
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.PPERequestItem", b =>
                {
                    b.HasOne("Harmoni360.Domain.Entities.PPERequest", "Request")
                        .WithMany("RequestItems")
                        .HasForeignKey("RequestId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Request");
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.RiskAssessment", b =>
                {
                    b.HasOne("Harmoni360.Domain.Entities.User", "ApprovedBy")
                        .WithMany()
                        .HasForeignKey("ApprovedById")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Harmoni360.Domain.Entities.User", "Assessor")
                        .WithMany()
                        .HasForeignKey("AssessorId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("Harmoni360.Domain.Entities.Hazard", "Hazard")
                        .WithMany("RiskAssessments")
                        .HasForeignKey("HazardId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ApprovedBy");

                    b.Navigation("Assessor");

                    b.Navigation("Hazard");
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.RoleModulePermission", b =>
                {
                    b.HasOne("Harmoni360.Domain.Entities.User", "GrantedByUser")
                        .WithMany()
                        .HasForeignKey("GrantedByUserId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("Harmoni360.Domain.Entities.ModulePermission", "ModulePermission")
                        .WithMany("RoleModulePermissions")
                        .HasForeignKey("ModulePermissionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Harmoni360.Domain.Entities.Role", "Role")
                        .WithMany("RoleModulePermissions")
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("GrantedByUser");

                    b.Navigation("ModulePermission");

                    b.Navigation("Role");
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.Security.SecurityAuditLog", b =>
                {
                    b.HasOne("Harmoni360.Domain.Entities.Security.SecurityIncident", "RelatedIncident")
                        .WithMany()
                        .HasForeignKey("RelatedIncidentId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("Harmoni360.Domain.Entities.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("RelatedIncident");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.Security.SecurityControl", b =>
                {
                    b.HasOne("Harmoni360.Domain.Entities.User", "ImplementedBy")
                        .WithMany()
                        .HasForeignKey("ImplementedById")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("Harmoni360.Domain.Entities.Security.SecurityIncident", "RelatedIncident")
                        .WithMany()
                        .HasForeignKey("RelatedIncidentId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("Harmoni360.Domain.Entities.User", "ReviewedBy")
                        .WithMany()
                        .HasForeignKey("ReviewedById")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("ImplementedBy");

                    b.Navigation("RelatedIncident");

                    b.Navigation("ReviewedBy");
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.Security.SecurityIncident", b =>
                {
                    b.HasOne("Harmoni360.Domain.Entities.User", "AssignedTo")
                        .WithMany()
                        .HasForeignKey("AssignedToId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("Harmoni360.Domain.Entities.User", "Investigator")
                        .WithMany()
                        .HasForeignKey("InvestigatorId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("Harmoni360.Domain.Entities.User", "Reporter")
                        .WithMany()
                        .HasForeignKey("ReporterId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.OwnsOne("Harmoni360.Domain.ValueObjects.GeoLocation", "GeoLocation", b1 =>
                        {
                            b1.Property<int>("SecurityIncidentId")
                                .HasColumnType("integer");

                            b1.Property<double>("Latitude")
                                .HasColumnType("decimal(10,8)")
                                .HasColumnName("Latitude");

                            b1.Property<double>("Longitude")
                                .HasColumnType("decimal(11,8)")
                                .HasColumnName("Longitude");

                            b1.HasKey("SecurityIncidentId");

                            b1.ToTable("SecurityIncidents");

                            b1.WithOwner()
                                .HasForeignKey("SecurityIncidentId");
                        });

                    b.Navigation("AssignedTo");

                    b.Navigation("GeoLocation");

                    b.Navigation("Investigator");

                    b.Navigation("Reporter");
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.Security.SecurityIncidentAttachment", b =>
                {
                    b.HasOne("Harmoni360.Domain.Entities.Security.SecurityIncident", "SecurityIncident")
                        .WithMany("Attachments")
                        .HasForeignKey("SecurityIncidentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("SecurityIncident");
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.Security.SecurityIncidentInvolvedPerson", b =>
                {
                    b.HasOne("Harmoni360.Domain.Entities.User", "Person")
                        .WithMany()
                        .HasForeignKey("PersonId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("Harmoni360.Domain.Entities.Security.SecurityIncident", "SecurityIncident")
                        .WithMany("InvolvedPersons")
                        .HasForeignKey("SecurityIncidentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Person");

                    b.Navigation("SecurityIncident");
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.Security.SecurityIncidentResponse", b =>
                {
                    b.HasOne("Harmoni360.Domain.Entities.User", "Responder")
                        .WithMany()
                        .HasForeignKey("ResponderId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("Harmoni360.Domain.Entities.Security.SecurityIncident", "SecurityIncident")
                        .WithMany("Responses")
                        .HasForeignKey("SecurityIncidentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Responder");

                    b.Navigation("SecurityIncident");
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.Security.ThreatAssessment", b =>
                {
                    b.HasOne("Harmoni360.Domain.Entities.User", "AssessedBy")
                        .WithMany()
                        .HasForeignKey("AssessedById")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("Harmoni360.Domain.Entities.Security.SecurityIncident", "SecurityIncident")
                        .WithMany()
                        .HasForeignKey("SecurityIncidentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("AssessedBy");

                    b.Navigation("SecurityIncident");
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.Training", b =>
                {
                    b.OwnsOne("Harmoni360.Domain.ValueObjects.GeoLocation", "GeoLocation", b1 =>
                        {
                            b1.Property<int>("TrainingId")
                                .HasColumnType("integer");

                            b1.Property<double>("Latitude")
                                .HasPrecision(10, 8)
                                .HasColumnType("double precision")
                                .HasColumnName("Latitude");

                            b1.Property<double>("Longitude")
                                .HasPrecision(11, 8)
                                .HasColumnType("double precision")
                                .HasColumnName("Longitude");

                            b1.HasKey("TrainingId");

                            b1.ToTable("Trainings");

                            b1.WithOwner()
                                .HasForeignKey("TrainingId");
                        });

                    b.Navigation("GeoLocation");
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.TrainingAttachment", b =>
                {
                    b.HasOne("Harmoni360.Domain.Entities.TrainingParticipant", "SubmittedByParticipant")
                        .WithMany()
                        .HasForeignKey("SubmittedByParticipantId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("Harmoni360.Domain.Entities.Training", "Training")
                        .WithMany("Attachments")
                        .HasForeignKey("TrainingId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("SubmittedByParticipant");

                    b.Navigation("Training");
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.TrainingCertification", b =>
                {
                    b.HasOne("Harmoni360.Domain.Entities.TrainingCertification", "RenewedFromCertificate")
                        .WithMany()
                        .HasForeignKey("RenewedFromCertificateId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("Harmoni360.Domain.Entities.Training", "Training")
                        .WithMany("Certifications")
                        .HasForeignKey("TrainingId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("RenewedFromCertificate");

                    b.Navigation("Training");
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.TrainingComment", b =>
                {
                    b.HasOne("Harmoni360.Domain.Entities.TrainingComment", "ParentComment")
                        .WithMany()
                        .HasForeignKey("ParentCommentId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Harmoni360.Domain.Entities.Training", "Training")
                        .WithMany("Comments")
                        .HasForeignKey("TrainingId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ParentComment");

                    b.Navigation("Training");
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.TrainingParticipant", b =>
                {
                    b.HasOne("Harmoni360.Domain.Entities.Training", "Training")
                        .WithMany("Participants")
                        .HasForeignKey("TrainingId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Training");
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.TrainingRequirement", b =>
                {
                    b.HasOne("Harmoni360.Domain.Entities.Training", "Training")
                        .WithMany("Requirements")
                        .HasForeignKey("TrainingId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Training");
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.UserRole", b =>
                {
                    b.HasOne("Harmoni360.Domain.Entities.Role", "Role")
                        .WithMany("UserRoles")
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Harmoni360.Domain.Entities.User", null)
                        .WithMany("UserRoles")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Role");
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.VaccinationRecord", b =>
                {
                    b.HasOne("Harmoni360.Domain.Entities.HealthRecord", "HealthRecord")
                        .WithMany("Vaccinations")
                        .HasForeignKey("HealthRecordId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("HealthRecord");
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.WorkPermit", b =>
                {
                    b.OwnsOne("Harmoni360.Domain.ValueObjects.GeoLocation", "GeoLocation", b1 =>
                        {
                            b1.Property<int>("WorkPermitId")
                                .HasColumnType("integer");

                            b1.Property<double>("Latitude")
                                .HasPrecision(10, 8)
                                .HasColumnType("double precision")
                                .HasColumnName("Latitude");

                            b1.Property<double>("Longitude")
                                .HasPrecision(11, 8)
                                .HasColumnType("double precision")
                                .HasColumnName("Longitude");

                            b1.HasKey("WorkPermitId");

                            b1.ToTable("WorkPermits");

                            b1.WithOwner()
                                .HasForeignKey("WorkPermitId");
                        });

                    b.Navigation("GeoLocation");
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.WorkPermitApproval", b =>
                {
                    b.HasOne("Harmoni360.Domain.Entities.WorkPermit", "WorkPermit")
                        .WithMany("Approvals")
                        .HasForeignKey("WorkPermitId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("WorkPermit");
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.WorkPermitAttachment", b =>
                {
                    b.HasOne("Harmoni360.Domain.Entities.WorkPermit", "WorkPermit")
                        .WithMany("Attachments")
                        .HasForeignKey("WorkPermitId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("WorkPermit");
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.WorkPermitHazard", b =>
                {
                    b.HasOne("Harmoni360.Domain.Entities.HazardCategory", "Category")
                        .WithMany()
                        .HasForeignKey("CategoryId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("Harmoni360.Domain.Entities.WorkPermit", "WorkPermit")
                        .WithMany("Hazards")
                        .HasForeignKey("WorkPermitId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Category");

                    b.Navigation("WorkPermit");
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.WorkPermitPrecaution", b =>
                {
                    b.HasOne("Harmoni360.Domain.Entities.WorkPermit", "WorkPermit")
                        .WithMany("Precautions")
                        .HasForeignKey("WorkPermitId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("WorkPermit");
                });

            modelBuilder.Entity("RolePermissions", b =>
                {
                    b.HasOne("Harmoni360.Domain.Entities.Permission", null)
                        .WithMany()
                        .HasForeignKey("PermissionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Harmoni360.Domain.Entities.Role", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("SecurityIncidentControls", b =>
                {
                    b.HasOne("Harmoni360.Domain.Entities.Security.SecurityControl", null)
                        .WithMany()
                        .HasForeignKey("SecurityControlId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Harmoni360.Domain.Entities.Security.SecurityIncident", null)
                        .WithMany()
                        .HasForeignKey("SecurityIncidentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("SecurityIncidentIndicators", b =>
                {
                    b.HasOne("Harmoni360.Domain.Entities.Security.SecurityIncident", null)
                        .WithMany()
                        .HasForeignKey("SecurityIncidentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Harmoni360.Domain.Entities.Security.ThreatIndicator", null)
                        .WithMany()
                        .HasForeignKey("ThreatIndicatorId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.Audits.Audit", b =>
                {
                    b.Navigation("Attachments");

                    b.Navigation("Comments");

                    b.Navigation("Findings");

                    b.Navigation("Items");
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.Audits.AuditFinding", b =>
                {
                    b.Navigation("Attachments");
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.Department", b =>
                {
                    b.Navigation("CorrectiveActions");

                    b.Navigation("Incidents");
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.EscalationRule", b =>
                {
                    b.Navigation("Actions");
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.Hazard", b =>
                {
                    b.Navigation("Attachments");

                    b.Navigation("MitigationActions");

                    b.Navigation("Reassessments");

                    b.Navigation("RiskAssessments");
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.HazardCategory", b =>
                {
                    b.Navigation("HazardTypes");

                    b.Navigation("Hazards");
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.HazardType", b =>
                {
                    b.Navigation("Hazards");
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.HealthRecord", b =>
                {
                    b.Navigation("EmergencyContacts");

                    b.Navigation("HealthIncidents");

                    b.Navigation("MedicalConditions");

                    b.Navigation("Vaccinations");
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.Incident", b =>
                {
                    b.Navigation("Attachments");

                    b.Navigation("CorrectiveActions");

                    b.Navigation("InvolvedPersons");
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.IncidentCategory", b =>
                {
                    b.Navigation("Incidents");
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.IncidentLocation", b =>
                {
                    b.Navigation("Incidents");
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.Inspections.Inspection", b =>
                {
                    b.Navigation("Attachments");

                    b.Navigation("Comments");

                    b.Navigation("Findings");

                    b.Navigation("Items");
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.Inspections.InspectionComment", b =>
                {
                    b.Navigation("Replies");
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.Inspections.InspectionFinding", b =>
                {
                    b.Navigation("Attachments");
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.ModulePermission", b =>
                {
                    b.Navigation("RoleModulePermissions");
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.PPECategory", b =>
                {
                    b.Navigation("PPEItems");
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.PPEItem", b =>
                {
                    b.Navigation("AssignmentHistory");

                    b.Navigation("Inspections");
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.PPERequest", b =>
                {
                    b.Navigation("RequestItems");
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.PPESize", b =>
                {
                    b.Navigation("PPEItems");
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.PPEStorageLocation", b =>
                {
                    b.Navigation("PPEItems");
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.Role", b =>
                {
                    b.Navigation("RoleModulePermissions");

                    b.Navigation("UserRoles");
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.Security.SecurityIncident", b =>
                {
                    b.Navigation("Attachments");

                    b.Navigation("InvolvedPersons");

                    b.Navigation("Responses");
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.Training", b =>
                {
                    b.Navigation("Attachments");

                    b.Navigation("Certifications");

                    b.Navigation("Comments");

                    b.Navigation("Participants");

                    b.Navigation("Requirements");
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.User", b =>
                {
                    b.Navigation("UserRoles");
                });

            modelBuilder.Entity("Harmoni360.Domain.Entities.WorkPermit", b =>
                {
                    b.Navigation("Approvals");

                    b.Navigation("Attachments");

                    b.Navigation("Hazards");

                    b.Navigation("Precautions");
                });
#pragma warning restore 612, 618
        }
    }
}
