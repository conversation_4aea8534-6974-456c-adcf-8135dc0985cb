{"extends": "./tsconfig.json", "compilerOptions": {"strict": false, "noImplicitAny": false, "strictNullChecks": false, "strictFunctionTypes": false, "strictPropertyInitialization": false, "noImplicitReturns": false, "noImplicitThis": false, "noImplicitOverride": false, "skipLibCheck": true}, "include": ["src"], "exclude": ["src/test/**/*", "src/**/*.test.ts", "src/**/*.test.tsx", "src/**/*.spec.ts", "src/**/*.spec.tsx"]}